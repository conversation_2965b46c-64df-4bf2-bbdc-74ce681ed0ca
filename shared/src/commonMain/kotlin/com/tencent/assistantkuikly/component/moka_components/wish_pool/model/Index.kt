package com.tencent.assistantkuikly.component.moka_components.wish_pool.model

/** 心愿物品 */
data class WishProperty (
    /** 心愿物品 ID */
    val id: String = "",
    /** 物品名称  */
    val name: String,
    /** 物品描述 */
    val desc: String,
    /** 物品图片 */
    val cover: String,
    /** 物品总许愿点数 */
    val wishPoints: String
)

/** 我的心愿物品 */
data class MyWishProperty (
    /** 概率 */
    val probability: String,
    /** 已投入心愿点 */
    val currentWishPoints: Int
)

/** 我的心愿信息 */
data class MyWishInfo (
    /** 我的心愿点 */
    val myWishPoints: Int,
    /** 本期心愿单 */
    val currentWishProperties: List<MyWishProperty>
)

/** 游戏信息 */
data class GameInfo(
    val appID: String,
    val name: String,
    val icon: String
)

/** 组件 Inject 配置 */
data class ComponentInject (
    val showLoadingToast: (String) -> Unit,
    val showSuccessToast: (String) -> Unit,
    val showFailToast: (String) -> Unit
)

/** 许愿记录 */
data class MyWishRecord (
    /** 许愿时间戳 */
    val time: String,
    /** 许愿物品 */
    val property: WishProperty,
    /** 投入心愿点 */
    val wishPoints: Int,
    /** 许愿结果 */
    val wishResult: Int
)

/** 用户信息 */
data class UserInfo (
    /** 用户名 */
    val name: String,
    /** 用户头像 */
    val icon: String
)

/** 许愿记录 */
data class WishResult(
    /** 许愿时间戳 */
    val time: String,
    /** 许愿物品 */
    val property: WishProperty,
    /** 中奖用户 */
    val rewardUser: UserInfo
)

/** 许愿池 */
data class WishPool (
    /** 心愿池 ID */
    val id: String,
    /** 许愿时间 */
    val time: String,
    /** 心愿池物品 */
    val wishProperties: List<WishProperty>,
    /** 是否可以许愿 */
    val canWish: Boolean
)


/** 许愿活动信息 */
data class WishActInfo (
    /** 许愿活动 ID */
    val id: String,
    /** 许愿池 */
    val pools: List<WishPool>,
    /** 当前时间 */
    val currentDate: String,
    /** 开始时间 */
    val startDate: String,
    /** 结束时间 */
    val endDate: String,
    /** 每日许愿开始时间 */
    val dailyWishStartTime: String,
    /** 每日许愿结束时间 */
    val dailyWishEndTime: String,
    /** 每日许愿开奖时间 */
    val dailyWishPublishTime: String,
    /** 规则，每消耗多少元返回一个心愿点 */
    val perPrice: String
)
