package com.tencent.assistantkuikly.component.moka_components.wish_pool

import com.tencent.assistantkuikly.base.Utils
import com.tencent.assistantkuikly.component.moka_components.domains.utils.isTestEnv
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.GetMyTodayWishDataReq
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.GetMyTodayWishDataResp
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.GetMyWishResultReq
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.GetMyWishResultResp
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.GetWishPoolDataReq
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.GetWishPoolDataRespData
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.IAppInfo
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.IMyWishData
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.IWishPoolActivityBaseInfo
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.IWishPoolActivityInfo
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.IWishPropertyInfo
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.IWishResult
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.getMyTodayWishData
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.getMyWishResult
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.getWishPoolData
import com.tencent.assistantkuikly.component.yybgameinfoauthsdk.core.gameAuthorizeV2
import com.tencent.assistantkuikly.component.yybgameinfoauthsdk.core.getGameAuthorizeStatus
import com.tencent.assistantkuikly.component.yybgameinfoauthsdk.core.ifHasAuthorized
import com.tencent.assistantkuikly.component.yybgameinfoauthsdk.type.GameAuthorizeReturnCode
import com.tencent.assistantkuikly.component.yybsdk.modules.actcommonreceive.IActCommonReceive
import com.tencent.assistantkuikly.component.yybsdk.modules.actcommonreceive.IActCommonReceive.ShowResult
import com.tencent.assistantkuikly.component.yybsdk.modules.receiveresult.ResultDrawer
import com.tencent.assistantkuikly.component.yybsdk.modules.receiveresult.newgood.NewGoodReceiveDrawer
import com.tencent.assistantkuikly.core.UserEngine
import com.tencent.assistantkuikly.core.config.ActivityMappingInfoManager
import com.tencent.assistantkuikly.core.message.MessageCenter
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.log.KLog
import com.tencent.kuikly.core.nvi.serialization.json.JSONArray
import com.tencent.kuikly.core.nvi.serialization.json.JSONObject
import com.tencent.kuikly.core.reactive.ReactiveObserver.Companion.observableList
import com.tencent.kuikly.core.reactive.handler.observable

private const val TAG = "WishPoolViewModel"

class WishPoolViewModel {
    private var config = WishPoolConfig()
    var showWishListDialog by observable(false)
    var showWishRewardDialog by observable(false)
    var showWishHistoryDialog by observable(false)
    var showWishConfirmDialog by observable(false)
    var showWishAllConfirmDialog by observable(false)
    /** 许愿墙*/
    var wishWallPanels by observableList<WishWallPanel>()
    /** 是否可以进行许愿*/
    var canWish by observable(true)
    /** 参与活动游戏*/
    val playingGames by observableList<IAppInfo>()
    /** 许愿商品*/
    var wishProperties by observableList<List<IMyWishData>>()
    /** 剩余心愿点 */
    var leftWishPoint by observable(-1)
    /** 是否显示登录*/
    var showLogin by observable(false)
    /** 基本信息*/
    var baseInfo by observable(IWishPoolActivityBaseInfo())
    /** 当前许愿池下标*/
    var currentPageIndex: Int = 0
    /** 选中的商品信息 */
    var selectedProperty by observable(IWishPropertyInfo())
    /** 许愿池信息 */
    var wishPoolActivityInfo by observable(IWishPoolActivityInfo())
    /** 许愿结果 */
    var wishResult by observableList<IWishResult>()
    /** 活动 */
    var wishActivityId: String = ""
    /** 服务器时间戳 */
    var serverTime by observable(0L)
    /** 用户充值金额 */
    var userRechargeAmount by observable(-1)
    /** 是否需要游戏授权*/
    var needAuthGames by observable(false)
    /** 订阅图片*/
    var subscribedIcon by observable("")
    var paramDialog by observable(false)
    var paramDialogContent: ViewBuilder = {}

    /** 是否为增量更新模式 */
    private var isIncrementalUpdate = false

    fun created(config: WishPoolConfig) {
        this.config = config
        refreshLoginState()
        val component = ActivityMappingInfoManager.getActivityMappingInfo().modIdMap[config.modId]
        wishActivityId = component?.componentId?.split("_")?.last() ?: ""
        getWishPoolData(
            req = GetWishPoolDataReq(
                componentType = "wish_poll",
                componentId = config.reportParams.componentId,
                modId = config.modId,
                step_num = 1,
                wish_activity_id = wishActivityId
            ), onError = {
                Utils.BridgeModule().toast(it)
            },
            callback = {
                handleOnReceiveWishPoolData(it)
                getMyTodayWishData()
                getMyWishResult()
            })
    }

    private fun handleOnReceiveWishPoolData(it: GetWishPoolDataRespData) {
        serverTime = it.serverTime
        wishPoolActivityInfo = it.data
        playingGames.clear()
        playingGames.addAll(it.appInfo)
        // 检查游戏授权状态
        checkGameAuthorize()
        wishWallPanels.clear()
        wishWallPanels.addAll(it.data.wishPoolList.map { wishPoolItem ->
            WishWallPanel().apply {
                canWish = wishPoolItem.canWish
                id = wishPoolItem.id
                dateTime = wishPoolItem.dateTime
                propertyList.addAll(wishPoolItem.propertyList.map { property ->
                    SelectableProperty().apply {
                        selected = false
                        this.property = property
                    }
                })
            }
        })
        val today = Utils.CalendarModule().formatTime(Utils.BridgeModule().currentTimeMs(), "yyyy-MM-dd")
        wishWallPanels.forEachIndexed { index, wishWallPanel ->
            if (Utils.CalendarModule().formatTime(wishWallPanel.dateTime * 1000, "yyyy-MM-dd") == today) {
                updateWishState()
                MessageCenter.postNotify("selectTargetWishPanel", JSONObject().apply {
                    put("pageIndex", index)
                })
            }
        }
        baseInfo = it.data.baseInfo
    }

    private fun getMyTodayWishData() {
        UserEngine.getUserInfo { userInfo ->
            if (userInfo.isLogin()) {
                getMyTodayWishData(GetMyTodayWishDataReq(componentType = "wish_poll", componentId = config.reportParams.componentId, modId = config.modId, wish_activity_id = wishActivityId)) { todayWishData ->
                    handleOnReceiveMyTodayWishData(todayWishData)
                }
            } else {
                wishProperties.clear()
            }
            showLogin = !userInfo.isLogin()
        }
    }

    private fun handleOnReceiveMyTodayWishData(todayWishData: GetMyTodayWishDataResp) {
        if (isIncrementalUpdate) {
            // 使用增量更新，只更新有变化的数据
            updateChangedDataOnly(todayWishData)
        } else {
            // 初始化时使用全量更新
            leftWishPoint = todayWishData.leftWishPoint
            userRechargeAmount = todayWishData.user_recharge_amount
            wishProperties.clear()
            wishProperties.addAll(todayWishData.data.map { wish ->
                wish.apply {
                    property = wishWallPanels.flatMap { it.propertyList }
                        .firstOrNull { it.property.id == wish.propertyId }?.property!!
                }
            }.chunked(3))
            // 刷新下注入心愿点状态
            updateWishState()
        }
    }

    private fun getMyWishResult() {
        UserEngine.getUserInfo { userInfo ->
            if (userInfo.isLogin()) {
                getMyWishResult(req = GetMyWishResultReq(componentType = "wish_poll",
                    componentId = config.reportParams.componentId,
                    modId = config.modId, wish_activity_id = wishActivityId)) { response ->
                    handleOnReceiveMyWishResult(response)
                }
            } else {
                wishResult.clear()
            }
        }
    }

    private fun handleOnReceiveMyWishResult(response: GetMyWishResultResp) {
        wishResult.clear()
        val poolId = response.wishPoolResult.wishPoolInfo.id
        val cacheKey = "act.wish-pool.${poolId}.${UserEngine.getOpenId()}"
        Utils.BridgeModule().getSetting(cacheKey) { cacheData ->
            val data = cacheData?.optString("data") ?: ""
            val hasNoAddress = response.wishPoolResult.wishResultList.any { item ->
                try {
                    val extJson = JSONObject(item.property.ext)
                    val address = extJson.optJSONArray("address") ?: JSONArray()
                    address.length() == 0
                } catch (e: Throwable) {
                    KLog.i(TAG, "parse ext err, " + e.stackTraceToString())
                    true
                }
            }
            KLog.i(TAG, "hasNoAddress: $hasNoAddress")
            if (hasNoAddress && data.isEmpty()) {
                wishResult.addAll(response.wishPoolResult.wishResultList)
                showWishRewardDialog = true

                Utils.BridgeModule().setSetting(cacheKey, poolId) {}
            }
        }
    }

    fun updateSubscribed() {
        subscribedIcon = config.subscribedImage
    }

    fun updateNotSubscribed() {
        subscribedIcon = config.unSubscribedImage
    }

    fun refreshLoginState() {
        UserEngine.getUserInfo {
            showLogin = !it.isLogin()
        }
    }

    /**
     * 心愿点注入成功后的增量更新
     * 需要同时更新用户数据和许愿墙的 all_wish_point 数据
     */
    fun updateAfterWishSuccess() {
        // 启用增量更新模式
        isIncrementalUpdate = true

        // 需要同时更新两个数据源：
        // 1. 用户相关数据 (getMyTodayWishData)
        // 2. 许愿墙的 all_wish_point 数据 (getWishPoolData)
        getWishPoolData(
            req = GetWishPoolDataReq(
                componentType = "wish_poll",
                componentId = config.reportParams.componentId,
                modId = config.modId,
                step_num = 1,
                wish_activity_id = wishActivityId
            ), onError = {
                Utils.BridgeModule().toast(it)
                // 出错时重置标志
                isIncrementalUpdate = false
            },
            callback = {
                handleOnReceiveWishPoolDataIncremental(it)
                getMyTodayWishData()
            })
    }

    /**
     * 增量更新时处理 getWishPoolData 响应
     * 只更新 wishWallPanels 中的 all_wish_point 字段，不重建整个数据结构
     * 不用clear() - addAll() 用替换 就不会闪
     */
    private fun handleOnReceiveWishPoolDataIncremental(data: GetWishPoolDataRespData) {
        data.data.wishPoolList.first().propertyList.first().all_wish_point = 10000
        data.data.wishPoolList.first().propertyList.first().icon = "https://ovact.iwan.yyb.qq.com/ovact_imgs/yyb_business_woa_1f0e95-5_1803926596_1753256384111680"

        // 只更新 wishWallPanels 中的 all_wish_point 字段
        data.data.wishPoolList.forEach { newWishPoolItem ->
            // 找到对应的 wishWallPanel
            val existingPanel = wishWallPanels.find { it.id == newWishPoolItem.id }
            existingPanel?.propertyList?.forEach { currentProperty ->
                currentProperty.property.id = newWishPoolItem.propertyList.first { it.id == currentProperty.property.id }.id
                currentProperty.property.name = newWishPoolItem.propertyList.first { it.id == currentProperty.property.id }.name
                currentProperty.property.icon = newWishPoolItem.propertyList.first { it.id == currentProperty.property.id }.icon
                currentProperty.property.all_wish_point = newWishPoolItem.propertyList.first { it.id == currentProperty.property.id }.all_wish_point
                currentProperty.property.ext = newWishPoolItem.propertyList.first { it.id == currentProperty.property.id }.ext
            }
        }
    }

    /**
     * 增量更新
     * 只更新有变化的数据字段
     */
    private fun updateChangedDataOnly(newTodayWishData: GetMyTodayWishDataResp) {
        // 更新剩余心愿点（如果有变化）
        if (leftWishPoint != newTodayWishData.leftWishPoint) {
            leftWishPoint = newTodayWishData.leftWishPoint
        }

        // 更新用户充值金额（如果有变化）
        if (userRechargeAmount != newTodayWishData.user_recharge_amount) {
            userRechargeAmount = newTodayWishData.user_recharge_amount
        }

        // 更新我的心愿物品列表（只在有变化时更新）
        val newWishProperties = newTodayWishData.data.map { wish ->
            wish.apply {
                property = wishWallPanels.flatMap { it.propertyList }
                    .firstOrNull { it.property.id == wish.propertyId }?.property!!
            }
        }.chunked(3)

        // 比较是否有变化，只有变化时才更新
        if (!areWishPropertiesEqual(wishProperties, newWishProperties)) {
            wishProperties.clear()
            wishProperties.addAll(newWishProperties)
        }

        // 刷新许愿状态
        updateWishState()

        // 5. 重置增量更新标志
        isIncrementalUpdate = false
    }

    /**
     * 比较两个心愿物品列表是否相等
     */
    private fun areWishPropertiesEqual(
        current: List<List<IMyWishData>>,
        new: List<List<IMyWishData>>
    ): Boolean {
        if (current.size != new.size) return false

        for (i in current.indices) {
            val currentGroup = current[i]
            val newGroup = new[i]

            if (currentGroup.size != newGroup.size) return false

            for (j in currentGroup.indices) {
                val currentItem = currentGroup[j]
                val newItem = newGroup[j]

                if (currentItem.propertyId != newItem.propertyId ||
                    currentItem.usedWishPoint != newItem.usedWishPoint ||
                    currentItem.allWishPoint != newItem.allWishPoint ||
                    currentItem.probability != newItem.probability) {
                    return false
                }
            }
        }
        return true
    }



    fun onSelectedProperty(propertyId: String) {
        KLog.i(TAG, "onSelectedProperty: $propertyId, currentPageIndex: $currentPageIndex")
        wishWallPanels[currentPageIndex].also {
            it.propertyList.forEach {  property ->
                KLog.i(TAG, "onSelectedProperty1: ${property.property}")
                property.selected = property.property?.id == propertyId
                if (property.selected) {
                    KLog.i(TAG, "onSelectedProperty2: ${property.property}")
                    selectedProperty = property.property!!
                }
            }
        }

        updateWishState()
    }

    fun onUnSelectedProperty(propertyId: String) {
        KLog.i(TAG, "onUnSelectedProperty: $propertyId")

        wishWallPanels.forEach {
            it.propertyList.forEach {  property ->
                property.selected = false
            }
        }

        updateWishState()
    }

    fun onPageSelected(pageIndex: Int) {
        KLog.i(TAG, "onPageSelected: $pageIndex")
        currentPageIndex = pageIndex
        updateWishState()
    }

    private fun updateWishState() {
        if (currentPageIndex > wishWallPanels.size) {
            return
        }
        UserEngine.getUserInfo {
            if (it.isLogin()) {
                canWish = wishWallPanels[currentPageIndex].canWish && leftWishPoint > 0
                        && wishWallPanels.flatMap { it.propertyList }.any { it.selected }
            } else {
                canWish = true
            }
        }
    }

    fun handleOnClickWishReward() {
        showWishRewardDialog = false
        KLog.i(TAG, "handleOnClickWishReward wishResult.size: ${wishResult.size}")
        if (wishResult.size > 1) {
            handleOnClickMyRewardList()
        } else if(wishResult.isNotEmpty()){
            paramDialog = false
            try {
                val property = wishResult.first().property
                val extJson = JSONObject(property.ext)
                val drawer = NewGoodReceiveDrawer("数据加载中").apply {
                    on(ResultDrawer.Event.CLOSE) {
                        paramDialog = false
                    }

                    on(ResultDrawer.Event.CONFIRM) {
                        paramDialog = false
                    }
                }
                drawer.setData(mapOf(
                    "isLoading" to false,
                    "receiveResult" to IActCommonReceive.ReceiveResult(
                        orderId = listOf(extJson.optString("order_id")),
                        showResult = ShowResult(
                            btnText = "确认",
                            title = "恭喜获得${property.name}",
                            desc = property.name,
                            img = property.icon
                        )
                    )
                ))

                paramDialogContent = drawer.content()
                paramDialog = true
            } catch (e: Throwable) {
                KLog.i(TAG, "handleOnClickWishReward error" + e.stackTraceToString())
            }
        }
    }

    fun handleOnClickWishAllPoints() {
        KLog.i(TAG, "handleOnClickWishAllPoints canWish: $canWish")
        UserEngine.getUserInfo {
            if (it.isLogin()) {
                if (!canWish) {
                    return@getUserInfo
                }
                showWishAllConfirmDialog = true
            } else {
                UserEngine.openLoginPage({
                    MessageCenter.postNotify("loginEvent")
                })
            }
        }
    }

    fun handleOnClickWishOnePoints() {
        KLog.i(TAG, "handleOnClickWishOnePoints canWish: $canWish")

        updateAfterWishSuccess()

        if (true) {
            return
        }

        UserEngine.getUserInfo {
            if (it.isLogin()) {
                if (!canWish) {
                    return@getUserInfo
                }
                showWishConfirmDialog = true
            } else {
                UserEngine.openLoginPage({
                    MessageCenter.postNotify("loginEvent")
                })
            }
        }
    }

    fun handleOnClickWishPoints() {
        UserEngine.getUserInfo {
            if (!it.isLogin()) {
                UserEngine.openLoginPage({
                    MessageCenter.postNotify("wish_pool_point_loginEvent")
                })
            } else if (needAuthGames) {
                handleOnAuthGames()
            }
        }
    }

    fun handleOnClickTotalSpending() {
        UserEngine.getUserInfo {
            if (!it.isLogin()) {
                UserEngine.openLoginPage({
                    MessageCenter.postNotify("wish_pool_point_loginEvent")
                })
            } else if (needAuthGames) {
                handleOnAuthGames()
            }
        }
    }

    fun handleOnClickMyWishList() {
        UserEngine.getUserInfo {
            if (!it.isLogin()) {
                UserEngine.openLoginPage({
                    MessageCenter.postNotify("loginEvent")
                })
            }
            else {
                showWishListDialog = true
            }
        }
    }

    fun handleOnClickWishHistory() {
        showWishHistoryDialog = true
    }

    fun handleOnClickMyRewardList() {
        config.events.filter { it.name == "act:component:wishPool:myRewardClick" }.forEach {
            MessageCenter.postNotify(it.method, JSONObject().apply {
                put("to", it.to)
            })
        }
    }

    private fun checkGameAuthorize() {
        UserEngine.getUserInfo {
            if (it.isLogin()) {
                val appIds = playingGames.map { it.appId.toString() }
                getGameAuthorizeStatus(appIds, "", isTestEnv(), 1) { response ->
                    KLog.i(TAG, "checkGameAuthorize response: $response")
                    val authorizeStatus = response.optJSONArray("games") ?: JSONArray()
                    if (ifHasAuthorized(appIds, authorizeStatus, "")) {
                        KLog.i(TAG, "checkGameAuthorize ifHasAuthorized: true")
                        needAuthGames = false
                    } else {
                        KLog.i(TAG, "checkGameAuthorize ifHasAuthorized: false")
                        needAuthGames = true
                    }
                }
            } else {
                needAuthGames = false
            }
        }
    }

    fun handleOnAuthGames() {
        val showDialog: (ViewBuilder) -> Unit = { viewBuilder ->
            KLog.i(TAG, "doObtain paramDialog: $paramDialog")
            paramDialog = false
            paramDialogContent = viewBuilder
            paramDialog = true
        }

        val closeDialog = {
            KLog.i(TAG, "doObtain close: $paramDialog")
            paramDialog = false
        }

        gameAuthorizeV2(
            playingGames.map { it.appId.toString() },
            listOf(),
            "",
            "",
            isTestEnv(),
            1,
            showDialog,
            closeDialog
        ) {
            KLog.i(TAG, "handleOnAuthGames response: $it")
            if (it.code == GameAuthorizeReturnCode.Success) {
                // 刷新
                created(config)
            }
        }
    }
}

class WishWallPanel {
    var canWish: Boolean = true
    /** 许愿池 ID */
    var id: String = ""
    /** 活动所属时间，以当天 12 点时间戳为准 */
    var dateTime: Long = 0L
    /** 许愿物品列表 */
    var propertyList by observableList<SelectableProperty>()
}

class SelectableProperty {
    var selected by observable(false)
    var property by observable(IWishPropertyInfo())
}