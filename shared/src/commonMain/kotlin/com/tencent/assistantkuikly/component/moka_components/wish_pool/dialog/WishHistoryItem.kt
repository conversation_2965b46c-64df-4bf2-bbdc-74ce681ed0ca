package com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog


import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.IWishResult
import com.tencent.assistantkuikly.report.ReportParams
import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ComposeAttr
import com.tencent.kuikly.core.base.ComposeEvent
import com.tencent.kuikly.core.base.ComposeView
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.base.ViewContainer
import com.tencent.kuikly.core.directives.velse
import com.tencent.kuikly.core.directives.vfor
import com.tencent.kuikly.core.directives.vif
import com.tencent.kuikly.core.log.KLog
import com.tencent.kuikly.core.views.Image
import com.tencent.kuikly.core.views.List
import com.tencent.kuikly.core.views.Text
import com.tencent.kuikly.core.views.View

internal class WishHistoryItemView :
    ComposeView<WishHistoryItemViewAttr, WishHistoryItemViewEvent>() {

    private val viewModel = WishHistoryDialogViewModel()

    override fun createAttr(): WishHistoryItemViewAttr {
        return WishHistoryItemViewAttr()
    }

    override fun createEvent(): WishHistoryItemViewEvent {
        return WishHistoryItemViewEvent()
    }

    override fun created() {
        super.created()
        viewModel.loadWishHistory(attr.config)
    }

    override fun body(): ViewBuilder {
        val ctx = this
        return {
            // 日期选择
            View {
                attr {
                    flexDirectionRow()
                    allCenter()
                    marginTop(18.wp())
                    marginBottom(16.wp())
                }
                // 左侧箭头
                vif({ ctx.viewModel.currentDateIndex != 0 }) {
                    View {
                        attr {
                            size(22.wp(), 12.wp())
                            allCenter()
                        }
                        Image {
                            attr {
                                alignSelfFlexEnd()
                                size(10.wp(), 12.wp())
                                src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/wyQ2P7P8.png")
                            }
                        }
                        event {
                            click {
                                KLog.d("history", "left")
                                ctx.viewModel.onDateChange(-1)
                            }
                        }
                    }
                }
                velse {
                    Image {
                        attr {
                            size(22.wp(), 12.wp())
                        }
                    }
                }
                Text {
                    attr {
                        marginLeft(13.wp())
                        marginRight(13.wp())
                        text(ctx.viewModel.currentDate.date)
                        color("#203564".parseColor())
                        fontWeight700()
                        fontSize(15.wp())
                        fontWeightBold()
                    }
                }
                // 右侧箭头
                vif({ ctx.viewModel.currentDateIndex != ctx.viewModel.dateRange.size - 1 }) {
                    View {
                        attr {
                            size(22.wp(), 12.wp())
                            allCenter()
                        }
                        Image {
                            attr {
                                alignSelfFlexStart()
                                size(10.wp(), 12.wp())
                                src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/FqIh0nRv.png")
                            }
                        }
                        event {
                            click {
                                KLog.d("history", "right")
                                ctx.viewModel.onDateChange(1)
                            }
                        }
                    }
                }
                velse {
                    Image {
                        attr {
                            size(22.wp(), 12.wp())
                        }
                    }
                }
            }

            // 表头 标题
            View {
                attr {
                    flexDirectionRow()
                    justifyContentFlexStart()
                    marginBottom(9.wp())
                }
                Text {
                    attr {
                        marginLeft(46.wp())
                        fontSize(13.wp())
                        text("奖品")
                        fontWeight500()
                        color("#203564".parseColor())
                    }
                }
                Text {
                    attr {
                        marginLeft(97.wp())
                        fontSize(13.wp())
                        text("中奖用户")
                        fontWeight500()
                        color("#203564".parseColor())
                    }
                }
            }
            vif({ ctx.viewModel.wishResultList.size == 0 && !ctx.viewModel.isLoading }) {
                View {
                    attr {
                        flex(1F)
                        allCenter()
                    }
                    Text {
                        attr {
                            text("暂无许愿结果")
                            color("#606D8B".parseColor())
                            fontSize(13.wp())
                            fontWeight400()
                        }
                    }
                }
            }
            velse {
                // 奖品列表
                List {
                    attr {
                        flex(1F)
                    }
                    vfor({ ctx.viewModel.wishResultList }) {
                        ctx.itemView(it).invoke(this)
                    }
                    vif({ ctx.viewModel.wishResultList.size != 0 }) {
                        // 分割线
                        View {
                            attr {
                                marginLeft(20.wp())
                                size(242.wp(), 1.wp())
                                backgroundColor("#2C2C2C0F".parseColor())
                            }
                        }
                    }

                    View {
                        attr {
                            height(21.wp())
                        }
                    }
                    vif({ ctx.viewModel.isFinished }) {
                        View {
                            attr {
                                allCenter()
                                marginBottom(20.wp())
                            }
                            Text {
                                attr {
                                    text("没有更多了")
                                    color("#606D8B".parseColor())
                                    fontSize(10.wp())
                                    fontWeight400()
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun itemView(data: IWishResult): ViewBuilder {
        return {
            View {
                attr {

                }
                // 分割线
                View {
                    attr {
                        marginLeft(20.wp())
                        size(242.wp(), 1.wp())
                        backgroundColor("#2C2C2C0F".parseColor())
                    }
                }
                View {
                    attr {
                        height(75.wp())
                        flexDirectionRow()
                        allCenter()
                        justifyContentFlexStart()
                    }
                    Image {
                        attr {
                            marginLeft(45.wp())
                            size(32.wp(), 42.37.wp())
                            src(data.property.icon)
                        }
                    }
                    Image {
                        attr {
                            marginLeft(96.wp())
                            size(26.15.wp(), 26.15.wp())
                            src(data.user.icon)
                            borderRadius(50.wp())
                        }
                    }
                    Text {
                        attr {
                            marginLeft(5.wp())
                            fontSize(10.wp())
                            color("#606D8B".parseColor())
                            fontWeight500()
                            text(data.user.name)
                        }
                    }
                }
            }
        }
    }
}

internal class WishHistoryItemViewAttr : ComposeAttr() {
    lateinit var config: WishHistoryItemConfig
}

internal class WishHistoryItemViewEvent : ComposeEvent() {

}

internal fun ViewContainer<*, *>.WishHistoryItem(init: WishHistoryItemView.() -> Unit) {
    addChild(WishHistoryItemView(), init)
}

data class WishHistoryItemConfig(
    /** 后端活动 id */
    var activityId: String = "",
    /** 活动开始时间戳 */
    var startTime: Long = 0L,
    /** 当前时间戳（服务器时间） */
    var currentTime: Long = 0L,
    var modID: String = "",
    /** 上报参数 */
    var reportParams: ReportParams = ReportParams()
)