package com.tencent.assistantkuikly.component.moka_components.wish_pool

import com.tencent.assistantkuikly.component.dialog.YybAlertDialog
import com.tencent.assistantkuikly.component.moka_components.task_obtain.events.TaskObtainEvent
import com.tencent.assistantkuikly.component.moka_components.wish_pool.components.CurrentWishProperties
import com.tencent.assistantkuikly.component.moka_components.wish_pool.components.DoWishProperty
import com.tencent.assistantkuikly.component.moka_components.wish_pool.components.MyReward
import com.tencent.assistantkuikly.component.moka_components.wish_pool.components.TotalSpending
import com.tencent.assistantkuikly.component.moka_components.wish_pool.components.WishHeader
import com.tencent.assistantkuikly.component.moka_components.wish_pool.components.WishPlayingGames
import com.tencent.assistantkuikly.component.moka_components.wish_pool.components.WishPoints
import com.tencent.assistantkuikly.component.moka_components.wish_pool.components.WishProperties
import com.tencent.assistantkuikly.component.moka_components.wish_pool.components.WishPublishTime
import com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog.MyWishDialog
import com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog.MyWishDialogConfig
import com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog.WishConfirmDialog
import com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog.WishConfirmDialogConfig
import com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog.WishHistoryDialog
import com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog.WishHistoryItemConfig
import com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog.WishRewardDialog
import com.tencent.assistantkuikly.core.message.MessageCenter
import com.tencent.assistantkuikly.report.ReportParams
import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.Border
import com.tencent.kuikly.core.base.BorderStyle
import com.tencent.kuikly.core.base.ColorStop
import com.tencent.kuikly.core.base.ComposeAttr
import com.tencent.kuikly.core.base.ComposeEvent
import com.tencent.kuikly.core.base.ComposeView
import com.tencent.kuikly.core.base.Direction
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.base.ViewContainer
import com.tencent.kuikly.core.module.NotifyModule
import com.tencent.kuikly.core.timer.CallbackRef
import com.tencent.kuikly.core.views.View

internal class WishPoolView: ComposeView<WishPoolViewAttr, WishPoolViewEvent>() {
    private val viewModel = WishPoolViewModel()
    private var callbacks = mutableListOf<CallbackRef>()

    override fun created() {
        super.created()
        viewModel.created(attr.config)
        callbacks.add(
            MessageCenter.addNotify(TaskObtainEvent.LOGIN_EVENT) {
                viewModel.refreshLoginState()
                viewModel.created(attr.config)
            }
        )

        callbacks.add(MessageCenter.addNotify("act:component:wishPool:refresh") {
            if (it?.optString("to") == attr.config.reportParams.componentId) {
                viewModel.created(attr.config)
            }
        })

        callbacks.add(MessageCenter.addNotify("act:component:wishPool:updateSubscribed") {
            if (it?.optString("to") == attr.config.reportParams.componentId) {
                viewModel.updateSubscribed()
            }
        })

        callbacks.add(MessageCenter.addNotify("act:component:wishPool:updateNotSubscribed") {
            if (it?.optString("to") == attr.config.reportParams.componentId) {
                viewModel.updateNotSubscribed()
            }
        })

        // 心愿点积分更新
        callbacks.add(
            MessageCenter.addNotify("wish_pool_user_wish_success") {
                // 注入心愿点
                viewModel.created(attr.config)
            }
        )

        // 登录事件 - 点击 我的心愿点 & 累计消费
        callbacks.add(
            MessageCenter.addNotify("wish_pool_point_loginEvent") {
                viewModel.refreshLoginState()
                viewModel.created(attr.config)
                if (viewModel.needAuthGames) {
                    viewModel.handleOnAuthGames()
                }
            }
        )
    }

    override fun viewDidUnload() {
        super.viewDidUnload()
        callbacks.forEach {
            acquireModule<NotifyModule>(NotifyModule.MODULE_NAME).removeCallback(it)
        }
    }

    override fun createEvent(): WishPoolViewEvent {
        return WishPoolViewEvent()
    }

    override fun createAttr(): WishPoolViewAttr {
        return WishPoolViewAttr()
    }

    override fun body(): ViewBuilder {
        val ctx = this
        return {
            attr {
                width(336.wp())
                margin(left = 19.5.wp(), right = 19.5.wp())
            }

            View {
                attr {
                    margin(top = -2.wp())
                    padding(bottom = 16.wp())
                    borderRadius(16.wp())
                    backgroundLinearGradient(Direction.TO_BOTTOM,
                        ColorStop("rgba(200, 249, 254, 1)".parseColor(), 0.0f),
                        ColorStop("rgba(227, 240, 255, 1)".parseColor(), 1.0f))
                    border(Border(1.wp(), BorderStyle.SOLID, "rgba(255, 255, 255, 1)".parseColor()))
                    width(336.wp())
                }

                // 头部消息
                WishHeader {
                    attr {
                        dailyWishStartTime = ctx.viewModel.baseInfo.wishStartTime
                        dailyWishEndTime = ctx.viewModel.baseInfo.wishEndTime
                    }

                    event {
                        onRefresh {
                            ctx.viewModel.created(ctx.attr.config)
                        }
                    }
                }

                // 参与游戏
                WishPlayingGames {
                    attr {
                        games = ctx.viewModel.playingGames
                    }
                }

                // 心愿公布时间
                WishPublishTime {
                    attr {
                        dailyWishPublishTime = ctx.viewModel.baseInfo.wishLotteryTime
                        subscribeIcon = ctx.viewModel.subscribedIcon
                    }
                }

                View {
                    attr {
                        width(304.wp())
                        alignSelfCenter()
                        margin(top = 13.wp(), bottom = 7.wp())
                        backgroundColor("rgba(214, 229, 245, 1)".parseColor())
                        borderRadius(12.wp())
                    }

                    // 心愿墙
                    WishProperties {
                        attr {
                            walls = ctx.viewModel.wishWallPanels
                        }

                        event {
                            onSelectedProperty {
                                ctx.viewModel.onSelectedProperty(it)
                            }

                            onUnSelectedProperty {
                                ctx.viewModel.onUnSelectedProperty(it)
                            }

                            onPageSelected {
                                ctx.viewModel.onPageSelected(it)
                            }
                        }
                    }

                    // 本期心愿
                    CurrentWishProperties {
                        attr {
                            showLogin = ctx.viewModel.showLogin
                            needAuthGames = ctx.viewModel.needAuthGames
                            emptyTips = "本期暂未许愿，快去注入心愿点吧～"
                            unAuthTips = "您还未授权，快去授权查看我的心愿吧~"
                            unLoginTips = "您还未登录，快去登录查看我的心愿吧~"
                            wishProperties = ctx.viewModel.wishProperties
                        }

                        event {
                            showHistoryWishProperties {
                                ctx.viewModel.handleOnClickWishHistory()
                            }
                        }
                    }

                    // 注入心愿点
                    DoWishProperty {
                        attr {
                            wishable = ctx.viewModel.canWish
                            leftWishPoint = ctx.viewModel.leftWishPoint
                        }

                        event {
                            onClickWishAllPoints {
                                ctx.viewModel.handleOnClickWishAllPoints()
                            }

                            onClickWishOnePoints {
                                ctx.viewModel.handleOnClickWishOnePoints()
                            }
                        }
                    }
                }

                // 心愿分
                WishPoints {
                    attr {
                        points = ctx.viewModel.leftWishPoint
                        showLogin = ctx.viewModel.showLogin
                        needAuthGames = ctx.viewModel.needAuthGames
                    }

                    event {
                        onShowWishDetail {
                            ctx.viewModel.handleOnClickMyWishList()
                        }
                        onClickPoints {
                            ctx.viewModel.handleOnClickWishPoints()
                        }
                    }
                }

                // 累计消费
                TotalSpending {
                    attr {
                        points = ctx.viewModel.userRechargeAmount
                        showLogin = ctx.viewModel.showLogin
                        needAuthGames = ctx.viewModel.needAuthGames
                    }
                    event {
                        onClickPoints {
                            ctx.viewModel.handleOnClickTotalSpending()
                        }
                    }
                }

                // 我的奖品
                MyReward {
                    event {
                        onShowMyRewardList {
                            ctx.viewModel.handleOnClickMyRewardList()
                        }
                    }
                }
            }

            YybAlertDialog {
                attr {
                    inWindow(true)
                    showAlert(ctx.viewModel.showWishConfirmDialog)
                    customContentView(
                        WishConfirmDialog.builder(
                            config = WishConfirmDialogConfig(
                                wishID = ctx.viewModel.wishPoolActivityInfo.id,
                                modID = ctx.attr.config.modId,
                                wishPollId = if (ctx.viewModel.wishWallPanels.size >= ctx.viewModel.currentPageIndex+1) ctx.viewModel.wishWallPanels[ctx.viewModel.currentPageIndex].id else "",
                                wishProperty = ctx.viewModel.selectedProperty,
                                isUseAllPoints = false,
                                leftWishPoint = ctx.viewModel.leftWishPoint,
                                reportParams = ctx.attr.config.reportParams
                            ),
                            onClose = {
                                ctx.viewModel.showWishConfirmDialog = false
                            }
                        )
                    )
                }
            }

            YybAlertDialog {
                attr {
                    inWindow(true)
                    showAlert(ctx.viewModel.showWishAllConfirmDialog)
                    customContentView(
                        WishConfirmDialog.builder(
                            config = WishConfirmDialogConfig(
                                wishID = ctx.viewModel.wishPoolActivityInfo.id,
                                modID = ctx.attr.config.modId,
                                wishPollId = if (ctx.viewModel.wishWallPanels.size >= ctx.viewModel.currentPageIndex+1) ctx.viewModel.wishWallPanels[ctx.viewModel.currentPageIndex].id else "",
                                wishProperty = ctx.viewModel.selectedProperty,
                                isUseAllPoints = true,
                                leftWishPoint = ctx.viewModel.leftWishPoint,
                                reportParams = ctx.attr.config.reportParams
                            ),
                            onClose = {
                                ctx.viewModel.showWishAllConfirmDialog = false
                            }
                        )
                    )
                }
            }

            YybAlertDialog {
                attr {
                    inWindow(true)
                    showAlert(ctx.viewModel.showWishHistoryDialog)
                    customContentView(
                        WishHistoryDialog.builder(
                            wishHistoryItemConfig = WishHistoryItemConfig(
                                activityId = ctx.viewModel.wishActivityId,
                                startTime = ctx.viewModel.wishPoolActivityInfo.startTime,
                                currentTime = ctx.viewModel.serverTime,
                                modID = ctx.attr.config.modId,
                                reportParams = ctx.attr.config.reportParams
                            ),
                            onClose = {
                                ctx.viewModel.showWishHistoryDialog = false
                            })
                    )
                }
            }
            YybAlertDialog {
                attr {
                    inWindow(true)
                    showAlert(ctx.viewModel.showWishListDialog)
                    customContentView(
                        MyWishDialog.builder(
                            myWishDialogConfig = MyWishDialogConfig(
                                activityId = ctx.viewModel.wishActivityId,
                                modId = ctx.attr.config.modId,
                                reportParams = ctx.attr.config.reportParams
                            ),
                            onClose = {
                                ctx.viewModel.showWishListDialog = false
                            }
                        )
                    )
                }
            }

            YybAlertDialog {
                attr {
                    inWindow(true)
                    showAlert(ctx.viewModel.showWishRewardDialog)
                    customContentView(
                        WishRewardDialog.builder(
                            wishResult = ctx.viewModel.wishResult,
                            onClose = {
                                ctx.viewModel.showWishRewardDialog = false
                            },
                            onClick = {
                                ctx.viewModel.handleOnClickWishReward()
                            }
                        )
                    )
                }
            }

            // 通用弹窗
            YybAlertDialog {
                attr {
                    inWindow(true)
                    showAlert(ctx.viewModel.paramDialog)
                    customContentView(ctx.viewModel.paramDialogContent)
                }
            }
        }
    }
}


internal class WishPoolViewAttr : ComposeAttr() {
    var config = WishPoolConfig()
}

internal class WishPoolViewEvent : ComposeEvent() {

}

internal fun ViewContainer<*, *>.WishPool(init: WishPoolView.() -> Unit) {
    addChild(WishPoolView(), init)
}

data class WishPoolConfig(
    var modId: String = "",
    var unSubscribedImage: String = "https://yyb.qpic.cn/moka-imgs/1753187595381-aotqdralj3g.png",
    var subscribedImage: String = "https://yyb.qpic.cn/moka-imgs/1753189132485-z0wdx9ffudf.png",
    var events: List<WishPoolEvent> = emptyList(),
    var reportParams: ReportParams = ReportParams()
)

data class WishPoolEvent (
    val name: String = "",
    val to: String = "",
    val method: String = ""
)