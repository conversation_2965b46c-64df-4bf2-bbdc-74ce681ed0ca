package com.tencent.assistantkuikly.component.moka_components.wish_pool.components

import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.IMyWishData
import com.tencent.assistantkuikly.utils.format
import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ComposeView
import com.tencent.kuikly.core.base.ComposeAttr
import com.tencent.kuikly.core.base.ComposeEvent
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.base.ViewContainer
import com.tencent.kuikly.core.directives.velse
import com.tencent.kuikly.core.directives.vfor
import com.tencent.kuikly.core.directives.vif
import com.tencent.kuikly.core.reactive.handler.observable
import com.tencent.kuikly.core.reactive.handler.observableList
import com.tencent.kuikly.core.views.Image
import com.tencent.kuikly.core.views.Text
import com.tencent.kuikly.core.views.View

internal class CurrentWishPropertiesView: ComposeView<CurrentWishPropertiesViewAttr, CurrentWishPropertiesViewEvent>() {
    
    override fun createEvent(): CurrentWishPropertiesViewEvent {
        return CurrentWishPropertiesViewEvent()
    }

    override fun createAttr(): CurrentWishPropertiesViewAttr {
        return CurrentWishPropertiesViewAttr()
    }

    override fun body(): ViewBuilder {
        val ctx = this
        return {
            View {
                attr {
                    alignItemsCenter()
                    alignSelfCenter()
                    margin(top = 20.wp())
                    width(288.34.wp())
                    flexDirectionRow()
                }

                Text {
                    attr {
                        flex(1.0F)
                        fontSize(14.wp())
                        color("rgba(36, 38, 107, 1)".parseColor())
                        text("本期心愿单：")
                    }
                }

                Text {
                    attr {
                        fontSize(12.wp())
                        color("rgba(10, 97, 143, 1)".parseColor())
                        text("往期心愿瓶结果公示")
                    }
                    event {
                        click {
                            ctx.event.onShowHistoryWishProperties.invoke()
                        }
                    }
                }

                Image {
                    attr {
                        margin(left = 3.wp())
                        size(5.wp(), 8.wp())
                        src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/gPI84QVz.png")
                    }
                }
            }

            View {
                attr {
                    margin(left = 16.wp(), top = 10.wp())
                }

                vif({ ctx.attr.showLogin || ctx.attr.needAuthGames || ctx.attr.wishProperties.isEmpty() }) {
                    Text {
                        attr {
                            lineHeight(86.wp())
                            width(303.wp())
                            textAlignCenter()
                            fontSize(14.wp())
                            color("rgba(116, 116, 116, 1)".parseColor())
                            text(
                                if (ctx.attr.showLogin) ctx.attr.unLoginTips
                                else if (ctx.attr.needAuthGames) ctx.attr.unAuthTips
                                else ctx.attr.emptyTips
                            )
                        }
                    }
                }
                velse {
                    vfor({ctx.attr.wishProperties}) { properties ->
                        View {
                            attr {
                                flexDirectionRow()
                                marginBottom(10.wp())
                            }
                            for (property in properties) {
                                <EMAIL>(property).invoke(this)
                            }
                        }
                    }
                }
            }
        }
    }


    private fun wishPropertyItem(property: IMyWishData): ViewBuilder {
        return {
            View {
                attr {
                    margin(right = 12.wp())
                    size(80.wp(), 86.wp())
                }

                View {
                    attr {
                        borderRadius(10.wp())
                        margin(top = 6.wp())
                        size(80.wp(), 80.wp())
                        backgroundColor("rgba(211, 226, 243, 1)".parseColor())
                    }
                }

                View {
                    attr {
                        absolutePositionAllZero()
                        size(80.wp(), 86.wp())
                        alignItemsCenter()
                    }

                    Image {
                        attr {
                            size(40.16.wp(), 48.99.wp())
                            src(property.property?.icon ?: "")
                        }
                    }

                    Text {
                        attr {
                            margin(top = 2.wp())
                            fontSize(12.wp())
                            fontWeight500()
                            color("rgba(36, 38, 107, 1)".parseColor())
                            text(property.property?.name ?: "")
                        }
                    }

                    Text {
                        attr {
                            margin(top = 2.wp())
                            fontSize(10.wp())
                            color("rgba(26, 27, 75, 0.7)".parseColor())
                            text("当前概率${<EMAIL>(property.probability)}%")
                        }
                    }
                }
            }
        }
    }

    /**
     * 格式化概率
     * 小于0.01%，显示为 0.01%
     * 保留三位数
     */
    private fun formatProbability(probability: Double) : String{
        if (probability < 0.0001) {
            return "0.01%"
        }
        val numberStr = "$probability"
        // 如果 整数位 是 1位数，保留 2位小数，否则保留 1位小数
        return if(numberStr.split(".")[0].length == 1) {
            format((probability * 100).toFloat(), 2)
        } else {
            format((probability * 100).toFloat(), 1)
        }
    }
}


internal class CurrentWishPropertiesViewAttr : ComposeAttr() {
    var wishProperties by observableList<List<IMyWishData>>()
    var emptyTips by observable("")
    var unAuthTips by observable("")
    var unLoginTips by observable("")
    var showLogin by observable(false)
    var needAuthGames by observable(false)
}

internal class CurrentWishPropertiesViewEvent : ComposeEvent() {
    var onShowHistoryWishProperties: () -> Unit = {}

    fun showHistoryWishProperties(handle: ()-> Unit) {
        onShowHistoryWishProperties = handle
    }
}

internal fun ViewContainer<*, *>.CurrentWishProperties(init: CurrentWishPropertiesView.() -> Unit) {
    addChild(CurrentWishPropertiesView(), init)
}