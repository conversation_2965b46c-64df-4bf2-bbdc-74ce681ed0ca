package com.tencent.assistantkuikly.component.moka_components.wish_pool.components

import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.IAppInfo
import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ComposeAttr
import com.tencent.kuikly.core.base.ComposeEvent
import com.tencent.kuikly.core.base.ComposeView
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.base.ViewContainer
import com.tencent.kuikly.core.directives.vfor
import com.tencent.kuikly.core.directives.vif
import com.tencent.kuikly.core.reactive.handler.observableList
import com.tencent.kuikly.core.views.Image
import com.tencent.kuikly.core.views.List
import com.tencent.kuikly.core.views.Text
import com.tencent.kuikly.core.views.View

internal class WishPlayingGamesView: ComposeView<WishPlayingGamesViewAttr, WishPlayingGamesViewEvent>() {
    
    override fun createEvent(): WishPlayingGamesViewEvent {
        return WishPlayingGamesViewEvent()
    }

    override fun createAttr(): WishPlayingGamesViewAttr {
        return WishPlayingGamesViewAttr()
    }

    override fun body(): ViewBuilder {
        val ctx = this
        return {
            vif({ctx.attr.games.size > 1}) {
                View {
                    attr {
                        marginTop(13.wp())
                        flexDirectionRow()
                        alignItemsCenter()
                    }

                    Text {
                        attr {
                            margin(left = 16.wp())
                            text("参与游戏：")
                            fontSize(13.wp())
                            color("rgba(36, 38, 107, 1)".parseColor())
                            fontWeight500()
                        }
                    }

                    List {
                        attr {
                            margin(right = 16.wp())
                            flexDirectionRow()
                            flex(1.0F)
                            height(26.3.wp())
                        }

                        vfor({ctx.attr.games}) { game ->
                            Image {
                                attr {
                                    margin(right = 8.wp())
                                    size(26.3.wp(), 26.3.wp())
                                    src(game.appIcon)
                                }
                            }
                        }
                    }
                }
            }

            View {
                attr {
                    margin(top = 13.wp(), left = 16.wp())
                    size(304.wp(), 1.wp())
                    backgroundColor("rgba(0, 0, 0, 0.08)".parseColor())
                }
            }
        }
    }
}


internal class WishPlayingGamesViewAttr : ComposeAttr() {
    var games by observableList<IAppInfo>()
}

internal class WishPlayingGamesViewEvent : ComposeEvent() {
    
}

internal fun ViewContainer<*, *>.WishPlayingGames(init: WishPlayingGamesView.() -> Unit) {
    addChild(WishPlayingGamesView(), init)
}