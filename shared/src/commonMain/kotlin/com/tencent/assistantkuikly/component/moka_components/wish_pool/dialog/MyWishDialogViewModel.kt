package com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog

import com.tencent.assistantkuikly.base.Utils
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.GetMyWishListReq
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.WishRecord
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.getMyWishList
import com.tencent.assistantkuikly.component.moka_components.wish_pool.model.WishStatus
import com.tencent.assistantkuikly.component.moka_data_core.config.SUCCESS_CODE
import com.tencent.kuikly.core.reactive.handler.observable
import com.tencent.kuikly.core.reactive.handler.observableList

class MyWishDialogViewModel {
    var isLoading by observable(false)
    var finished by observable(false)
    private var currentPageNo by observable(0)
    var wishRecordList by observableList<WishRecord>()
    private lateinit var config: MyWishDialogConfig

    fun wishResultText(wishResult: Int): String {
        return when (wishResult) {
            WishStatus.NoResult.value -> "待开奖"
            WishStatus.Miss.value -> "未中奖"
            WishStatus.Winning.value -> "中奖"
            else -> ""
        }
    }

    fun loadMoreMyWishRecord() {
        if (finished) {
            return
        }
        loadMyWishRecord()
    }

    fun init(config: MyWishDialogConfig) {
        this.config = config
        loadMyWishRecord()
    }

    private fun loadMyWishRecord(pageNum: Int = 10) {
        isLoading = true
        finished = false
        if (currentPageNo == 0) {
            wishRecordList.clear()
        }
        getMyWishList(
            req = GetMyWishListReq(
                wish_activity_id = config.activityId,
                pageNo = currentPageNo,
                pageNum = pageNum
            ),
            componentID = config.reportParams.componentId,
            modID = config.modId,
            callback = {
                if (it.code != SUCCESS_CODE) {
                    Utils.BridgeModule().toast(it.msg)
                    finished = true
                    isLoading = false
                }
                wishRecordList.addAll(it.recordList)
                finished = !it.hasNext
                currentPageNo += 1
                isLoading = false
            }
        )
    }
}