package com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog

import com.tencent.assistantkuikly.base.Utils
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.UserWishReq
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.userWish
import com.tencent.assistantkuikly.component.moka_components.wish_pool.constants.SUCCESS_CODE
import com.tencent.assistantkuikly.component.moka_components.wish_pool.constants.WISH_INJECTING_SUCCESS
import com.tencent.assistantkuikly.component.moka_components.wish_pool.constants.WISH_INJECTING_TEXT
import com.tencent.assistantkuikly.core.message.MessageCenter
import com.tencent.kuikly.core.log.KLog

class WishConfirmDialogViewModel {

    private val TAG = "WishConfirmDialogViewModel"

    private var isHandlingConfirm = false
    private lateinit var config: WishConfirmDialogConfig


    fun init(config: WishConfirmDialogConfig){
        this.config = config
    }

    fun handleConfirm(onFinish: () -> Unit) {
        if (isHandlingConfirm) return
        isHandlingConfirm = true
        Utils.BridgeModule().toast(WISH_INJECTING_TEXT)

        val wishPoolId = config.wishPollId
        val propertyId = config.wishProperty.id
        if (wishPoolId.isEmpty() || propertyId.isEmpty()){
            KLog.e(TAG, "handleConfirm: 选中心愿物品信息异常，许愿池 ID: ${wishPoolId}, 心愿物品 ID: $propertyId")
            return
        }

        userWish(
            req = UserWishReq(
                wish_activity_id = config.wishID,
                wish_pool_id = wishPoolId,
                property_id = propertyId,
                use_all_point = config.isUseAllPoints
            ),
            componentID = config.reportParams.componentId,
            modID = config.modID,
            callback={
                if (it.code == SUCCESS_CODE) {
                    Utils.BridgeModule().toast(WISH_INJECTING_SUCCESS)
                    // 注入成功通知，减去对应积分
                    MessageCenter.postNotify("wish_pool_user_wish_success")
                } else {
                    Utils.BridgeModule().toast(it.msg)
                }
            }
        )
        // 回调 关闭弹窗
        onFinish()
        isHandlingConfirm = false
    }

}