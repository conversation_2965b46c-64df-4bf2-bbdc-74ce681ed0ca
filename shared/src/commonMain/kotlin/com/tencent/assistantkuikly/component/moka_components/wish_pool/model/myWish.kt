package com.tencent.assistantkuikly.component.moka_components.wish_pool.model

/** 用户信息 */
data class User (
    /** 用户昵称 */
    var name: String,
    /** 用户图标 */
    var icon: String
)

/** 许愿商品信息 */
data class WishPropertyInfo (
    /** 许愿商品 id */
    var id: String,
    /** 许愿商品名称 */
    var name: String,
    /** 许愿商品图标 */
    var icon: String,
    /** 许愿商品心愿点 */
    var allWishPoint: String,
    /** 许愿商品额外信息 */
    var ext: String
)

/** 单个心愿抽奖结果 */
data class WishLotteryResult (
    /** 许愿商品信息 */
    var property: WishPropertyInfo,
    /** 中奖用户信息 */
    var user: User
)

/** 许愿池信息 */
data class WishPoolInfo (
    /** 许愿池 id */
    var id: String,
    /** 活动开始时间（秒级时间戳） */
    var startTime: Int,
    /** 活动结束时间（秒级时间戳） */
    var endTime: Int,
    /** 许愿商品列表 */
    var propertyList: List<WishPropertyInfo>
)

/** 心愿池的抽奖结果 */
data class WishPoolResult (
    /** 许愿池信息 */
    var wishPool: WishPoolInfo,
    /** 所有的心愿抽奖结果 */
    var wishResultList: List<WishLotteryResult>
    )

enum class WishStatus(val value: Int) {
    /** 默认值 */
    Default(0),
    /** 未开奖 */
    NoResult(1),
    /** 已经开奖，但是未中奖 */
    Miss(2),
    /** 已经开奖，中奖了 */
    Winning(3),
}

/** 获取所有的心愿抽奖结果 */
data class GetAllWishResultRsp (
    /** 状态码 */
    var code: Int,
    /** 返回消息 */
    var msg: String,
    /** 所有的心愿池抽奖结果 */
    var wishPoolResultList: List<WishPoolResult>
)
