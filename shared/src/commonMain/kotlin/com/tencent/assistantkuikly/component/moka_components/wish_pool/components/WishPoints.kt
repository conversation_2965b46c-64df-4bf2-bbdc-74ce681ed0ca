package com.tencent.assistantkuikly.component.moka_components.wish_pool.components

import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ComposeView
import com.tencent.kuikly.core.base.ComposeAttr
import com.tencent.kuikly.core.base.ComposeEvent
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.base.ViewContainer
import com.tencent.kuikly.core.directives.velse
import com.tencent.kuikly.core.directives.vif
import com.tencent.kuikly.core.log.KLog
import com.tencent.kuikly.core.reactive.handler.observable
import com.tencent.kuikly.core.views.Image
import com.tencent.kuikly.core.views.Text
import com.tencent.kuikly.core.views.View

internal class WishPointsView: ComposeView<WishPointsViewAttr, WishPointsViewEvent>() {
    
    override fun createEvent(): WishPointsViewEvent {
        return WishPointsViewEvent()
    }

    override fun createAttr(): WishPointsViewAttr {
        return WishPointsViewAttr()
    }

    override fun body(): ViewBuilder {
        val ctx = this
        return {
            View {
                attr {
                    alignItemsCenter()
                    margin(top = 15.wp(), left = 16.wp())
                    width(320.wp())
                    flexDirectionRow()
                }

                Text {
                    attr {
                        fontSize(14.wp())
                        color("rgba(36, 38, 107, 1)".parseColor())
                        text("我的心愿点：")
                    }
                }

                vif({ctx.attr.showLogin || ctx.attr.needAuthGames}) {
                    Text {
                        attr {
                            fontSize(12.wp())
                            color("rgba(10, 97, 143, 1)".parseColor())
                            text(if (ctx.attr.showLogin) "登录后可查看" else "授权后查看")
                        }

                        event {
                            click {
                                ctx.event.onClickPoints.invoke()
                            }
                        }
                    }
                }
                velse {
                    Text {
                        attr {
                            fontWeight700()
                            fontSize(15.wp())
                            color("rgba(255, 0, 123, 1)".parseColor())
                            text(if (ctx.attr.points == -1) "" else "${ctx.attr.points}")
                        }
                    }
                }

                vif({ctx.attr.showLogin || ctx.attr.needAuthGames}) {
                    Image {
                        attr {
                            size(5.wp(), 8.wp())
                            src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/gPI84QVz.png")
                        }
                    }
                }

                Text {
                    attr {
                        flex(1.0F)
                        fontSize(12.wp())
                        textAlignRight()
                        color("rgba(10, 97, 143, 1)".parseColor())
                        text("我的心愿单")
                    }

                    event {
                        click {
                            ctx.event.onShowWishDetail.invoke()
                        }
                    }
                }

                Image {
                    attr {
                        margin(left = 3.wp(), right = 17.wp())
                        size(5.wp(), 8.wp())
                        src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/gPI84QVz.png")
                    }

                    event {
                        click {
                            ctx.event.onShowWishDetail.invoke()
                        }
                    }
                }
            }
        }
    }
}


internal class WishPointsViewAttr : ComposeAttr() {
    var points by observable(0)
    var showLogin by observable(false)
    var needAuthGames by observable(false)
}

internal class WishPointsViewEvent : ComposeEvent() {
    var onShowWishDetail: ()-> Unit = {}

    var onClickPoints: ()-> Unit = {}

    fun onShowWishDetail(handler: ()-> Unit) {
        onShowWishDetail = handler
    }

    fun onClickPoints(handle: ()->Unit) {
        onClickPoints = handle
    }
}

internal fun ViewContainer<*, *>.WishPoints(init: WishPointsView.() -> Unit) {
    addChild(WishPointsView(), init)
}