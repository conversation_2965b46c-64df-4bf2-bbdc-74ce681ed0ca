package com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog

import com.tencent.assistantkuikly.base.Utils
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.GetAllWishResultReq
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.IWishResult
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.getAllWishResult
import com.tencent.assistantkuikly.component.moka_data_core.config.SUCCESS_CODE
import com.tencent.kuikly.core.module.ICalendar
import com.tencent.kuikly.core.reactive.ReactiveObserver.Companion.observableList
import com.tencent.kuikly.core.reactive.handler.observable

class WishHistoryDialogViewModel {
    var wishResultList by observableList<IWishResult>()
    var currentDate by observable(DateItem())
    private lateinit var config: WishHistoryItemConfig
    var currentDateIndex by observable(0)
    var dateRange by observableList<DateItem>()
    var isFinished by observable(true)
    var isLoading by observable(false)
    private val cacheWishResults: MutableMap<String, MutableList<IWishResult>> = mutableMapOf()

    fun loadWishHistory(config: WishHistoryItemConfig) {
        this.config = config
        if (config.startTime == 0L || config.currentTime == 0L) {
            return
        }
        // 计算时间范围
        computeDateRange(config.startTime, config.currentTime)
        currentDateIndex = dateRange.size - 1
        currentDate = dateRange[currentDateIndex]
        // 发起请求 获取数据
        load()
    }


    private fun load() {
        val currentTimestamp = dateRange[currentDateIndex].timestamp
        if (currentTimestamp == 0L || isLoading) {
            return
        }

        wishResultList.clear()

        if (cacheWishResults.containsKey(currentTimestamp.toString())) {
            cacheWishResults[currentTimestamp.toString()]?.let { wishResultList.addAll(it) }
            return
        }

        isLoading = true
        isFinished = false

        getAllWishResult(
            req = GetAllWishResultReq(
                wish_activity_id = config.activityId,
//            wish_pool_time_list = dateRange.map { it.timestamp },
                wish_pool_time_list = listOf(convertToSeconds(currentTimestamp)),
            ),
            componentID = config.reportParams.componentId,
            modID = config.modID,
            callback = {
                if (it.code != SUCCESS_CODE) {
                    Utils.BridgeModule().toast(it.msg)
                    isLoading = false
                    isFinished = true
                }

                val cacheKey = currentTimestamp.toString()
                if (!cacheWishResults.containsKey(currentTimestamp.toString())) {
                    cacheWishResults[cacheKey] = mutableListOf()
                }
                if (it.wish_pool_result_list.isNotEmpty()) {
                    cacheWishResults[cacheKey]?.addAll(it.wish_pool_result_list[0].wishResultList)
                }
//            wishResultList.addAll(it.wish_pool_result_list[0].wishResultList)
                cacheWishResults[currentTimestamp.toString()]?.let { item ->
                    wishResultList.addAll(item)
                }
                isLoading = false
                isFinished = true
            })
    }

    fun onDateChange(direction: Int) {
        val newDateIndex = currentDateIndex + direction
        if (newDateIndex > dateRange.size - 1 || currentDateIndex + direction < 0) {
            // 索引不在往期日期集合的范围内
            return
        }

        currentDateIndex = newDateIndex
        currentDate = dateRange[currentDateIndex]
        load()
    }

    /**
     * 计算时间范围
     */
    private fun computeDateRange(startTime: Long, currentTime: Long) {
        val startTimeMs = convertToMilliseconds(startTime)
        val currentTimeMs = convertToMilliseconds(currentTime)

        val calendarModule = Utils.CalendarModule()

        // 创建起始日期的日历实例 方便实现日期加一天计算
        var calendar = calendarModule.newCalendarInstance(startTimeMs)

        // 结束时间的时间戳
        val endTimestamp = currentTimeMs


        while (calendar.timeInMillis() <= endTimestamp) {
            // 格式化当前日期为 yyyy-MM-dd
            val dateStr = calendarModule.formatTime(calendar.timeInMillis(), "yyyy-MM-dd")
            dateStr.split("-").let {
                val month = it[1]
                val day = it[2]
                // 添加日期项，格式 MM月dd日
                dateRange.add(
                    DateItem(
                        date = "${month}月${day}日",
                        timestamp = calendar.timeInMillis()
                    )
                )

                // 在当前日历实例上加一天
                calendar = calendar.add(ICalendar.Field.DAY_OF_MONTH, 1)
            }
        }
    }

    /**
     * 将时间戳转换成毫秒
     */
    private fun convertToMilliseconds(timestamp: Long): Long {
        return if (timestamp < 1_000_000_000_000L) {
            // 认为是秒级时间戳，乘以 1_000 转成微秒
            timestamp * 1_000L
        } else {
            // 认为是毫秒级时间戳
            timestamp
        }
    }

    /**
     * 将时间戳转换成秒
     */
    private fun convertToSeconds(timestamp: Long): Long {
        return if (timestamp < 1_000_000_000_000L) {
            // 认为是秒级时间戳，除以 1_000 转成秒
            timestamp
        } else {
            // 认为是毫秒级时间戳
            timestamp / 1_000L
        }
    }

}

data class DateItem(
    val date: String = "",
    val timestamp: Long = 0L
)
