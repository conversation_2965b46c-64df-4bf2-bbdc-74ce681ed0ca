package com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog

import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.IWishPropertyInfo
import com.tencent.assistantkuikly.report.ReportParams
import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ColorStop
import com.tencent.kuikly.core.base.Direction
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.views.Image
import com.tencent.kuikly.core.views.Text
import com.tencent.kuikly.core.views.View

/**
 * 注入心愿点 - 确认弹窗
 */
object WishConfirmDialog {

    private val viewModel = WishConfirmDialogViewModel()

    fun builder(config: WishConfirmDialogConfig, onClose: () -> Unit): ViewBuilder {
        viewModel.init(config)

        return {
            attr {
                allCenter()
            }

            event {
                click {}
            }

            // 关闭按钮
            Image {
                attr {
                    alignSelfFlexEnd()
                    size(23.wp(), 23.wp())
                    src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/3GXSCORV.png")
                }
                event {
                    click {
                        onClose.invoke()
                    }
                }
            }
            View {
                attr {
                    marginTop((-15).wp())
                    size(282.wp(), 270.wp())
                    allCenter()
                }

                // 背景
                View {
                    // 背景
                    attr {
                        absolutePosition(bottom = 0.wp())
                        size(282.wp(), 246.wp())
                        borderRadius(16.2.wp())
                        backgroundLinearGradient(
                            Direction.TO_BOTTOM,
                            ColorStop("#C3F8FE".parseColor(), 0F),
                            ColorStop("#F2FEFF".parseColor(), 0.15F)
                        )
                        allCenter()
                    }
                    Text {
                        attr {
                            marginTop(111.wp())
                            text("您的心愿是")
                            color("#203564".parseColor())
                            fontSize(14.wp())
                            fontWeight400()
                        }
                    }
                    Text {
                        attr {
                            marginTop(2.wp())
                            text(config.wishProperty.name)
                            color("#203564".parseColor())
                            fontSize(14.wp())
                            fontWeight400()
                        }
                    }
                    View {
                        attr {
                            height(31.wp())
                            allCenter()
                            flexDirectionRow()
                        }
                        Text {
                            attr {
                                text("将消耗")
                                color("#203564".parseColor())
                                fontSize(14.wp())
                                fontWeight400()
                            }
                        }
                        Text {
                            attr {
                                text(
                                    if (config.isUseAllPoints) {
                                        if (config.leftWishPoint <= 100) config.leftWishPoint.toString() else "100"
                                    } else "1"
                                )
                                color("#FF007B".parseColor())
                                fontSize(14.wp())
                                fontWeightBold()
                                fontWeight700()
                            }
                        }
                        Text {
                            attr {
                                text("个心愿点")
                                color("#203564".parseColor())
                                fontSize(14.wp())
                                fontWeight400()
                            }
                        }
                    }
                    Image {
                        attr {
                            marginTop(5.wp())
                            size(203.wp(), 42.wp())
                            src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/sXRWdGwz.png")
                        }
                        event {
                            click {
                                viewModel.handleConfirm(onFinish = { onClose.invoke() })
                            }
                        }
                    }
                }
                // 头部背景
                Image {
                    attr {
                        absolutePosition(top = 10.wp(), left = 88.wp(), right = 88.wp())
                        size(111.wp(), 135.wp())
                        src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/%E4%B8%AD%E5%A5%96%E5%A4%B4%E9%83%A8%E8%83%8C%E6%99%AF.png")
                    }
                }

            }
        }
    }
}

data class WishConfirmDialogConfig(
    /** 许愿活动 ID */
    val wishID: String,
    val modID: String,
    val wishPollId: String,
    val wishProperty: IWishPropertyInfo,
    val isUseAllPoints: Boolean,
    val leftWishPoint: Int,
    val reportParams: ReportParams
)