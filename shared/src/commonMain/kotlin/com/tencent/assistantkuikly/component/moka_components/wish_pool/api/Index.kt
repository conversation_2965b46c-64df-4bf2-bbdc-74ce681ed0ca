package com.tencent.assistantkuikly.component.moka_components.wish_pool.api

import com.tencent.assistantkuikly.base.Utils
import com.tencent.assistantkuikly.component.moka_data_core.config.SUCCESS_CODE
import com.tencent.assistantkuikly.component.moka_data_core.domain.user_cases.ReturnCodeConfigManager
import com.tencent.assistantkuikly.component.moka_data_core.domain.user_cases.requesters.ActExecAccessRequester
import com.tencent.assistantkuikly.component.moka_data_core.types.ActExecReq
import com.tencent.assistantkuikly.component.moka_data_core.types.BackendComponentInfo
import com.tencent.assistantkuikly.component.moka_data_core.types.InterfaceType
import com.tencent.assistantkuikly.component.utils.getTrpcFuncTet
import com.tencent.assistantkuikly.core.http.PageRequestEngine
import com.tencent.assistantkuikly.core.request.HttpProtocolInterceptor
import com.tencent.assistantkuikly.utils.map
import com.tencent.assistantkuikly.utils.toJSONArray
import com.tencent.kuikly.core.log.KLog
import com.tencent.kuikly.core.nvi.serialization.json.JSONArray
import com.tencent.kuikly.core.nvi.serialization.json.JSONException
import com.tencent.kuikly.core.nvi.serialization.json.JSONObject

private const val TAG = "Wish_Pool_api"

private const val componentType = "wish"

/** 查询许愿池全部数据 */
fun getWishPoolData(req: GetWishPoolDataReq, callback : (GetWishPoolDataRespData) -> Unit, onError : (String) -> Unit) {
    KLog.i(TAG, "getWishPoolData req: $req")
    val request = ActExecReq(
        componentInfo = BackendComponentInfo(
            componentID = req.componentId,
            componentType = req.componentType,
            modID = req.modId
        ),
        componentType = req.componentType,
        componentID = req.componentId,
        invocation = mapOf(
            "name" to "/trpc.component_plat.lotteryng.WishPool/GetWishPoolData",
            "data" to mapOf(
                "wish_activity_id" to req.wish_activity_id,
                // 步长,为0 则只返回当天,为1则返回昨天，今天，明天三天的数据
                "step_num" to req.step_num
            )
        ),
        qualifier_params = listOf()
    )
    ActExecAccessRequester().request(request) { response -> KLog.i(TAG, "getWishPoolData rsp: ${response.body} code: ${response.code} tip: ${response.tip}")

        if (response.code != SUCCESS_CODE) {
          onError(response.tip)
        }

        val rawData = response.body?.data?.data
        if (rawData.isNullOrBlank()) {
            // 数据为空，直接返回
            return@request
        }

        val data = try {
            JSONObject(rawData)
        } catch (e: JSONException) {
            KLog.e(TAG, "JSON解析失败: ${e.message}")
            return@request
        }

        if (data.keySet().isEmpty()) {
            return@request
        }

        val appList = data.optJSONArray("app_list")
        val activityInfoData = data.optJSONObject("data")
        val wishPoolList = activityInfoData?.optJSONArray("wish_pool_list")
        val baseInfo = activityInfoData?.optJSONObject("base_info") ?: JSONObject()
        val activityInfo = IWishPoolActivityInfo(
            id = activityInfoData?.optString("id") ?: "",
            startTime = activityInfoData?.optLong("start_time") ?: 0L,
            endTime = activityInfoData?.optLong("end_time") ?: 0L,
            baseInfo = IWishPoolActivityBaseInfo(
                wishStartTime = baseInfo.optString("wish_start_time", "00:00"),
                wishEndTime = baseInfo.optString("wish_end_time", "22:00"),
                wishLotteryTime = baseInfo.optString("wish_lottery_time", "23:00")
            ),
            wishPoolList = wishPoolList?.map { item ->
                IWishPoolInfo(
                    canWish = item.optBoolean("can_wish"),
                    id = item.optString("id"),
                    dateTime = item.optLong("date_time"),
                    propertyList = item.optJSONArray("property_list")?.map { property ->
                        IWishPropertyInfo(
                            id = property.optString("id"),
                            name = property.optString("name"),
                            icon = property.optString("icon"),
                            all_wish_point = property.optInt("all_wish_point"),
                            ext = property.optString("ext")
                        )
                    } ?: listOf()
                )
            } ?: listOf()
        )


        val serverTs = response.body.data.server_ts
        val serverTime = convertServerTime(serverTs)

        KLog.i(TAG, "getWishPoolData serverTime $serverTime")

        val responseData = GetWishPoolDataRespData(
            appInfo = appList?.map { app ->
                IAppInfo(
                    appId = app.optInt("app_id"),
                    appIcon = app.optString("app_icon"),
                    appName = app.optString("app_name"),
                )
            } ?: listOf(),
            data = activityInfo,
            serverTime = serverTime
        )

        callback(responseData)
    }
}


/**
 *
 * 将 server_ts -> 2025-07-21T13:10:06.684935588Z 转化为时间戳
 */

private fun convertServerTime(serverTs: String): Long {
    val split = serverTs.split("T")
    val normalFormatTime = split[0] + " " + split[1].split("Z")[0]
    val parseFormattedTime = Utils.CalendarModule()
        .parseFormattedTime(normalFormatTime, "yyyy-MM-dd HH:mm:ss")
    KLog.i(TAG, "getWishPoolData $parseFormattedTime")
    val serverTime = parseFormattedTime + 8 * 60 * 60 * 1000
    return serverTime
}

fun getMyTodayWishData(req: GetMyTodayWishDataReq, callback: (GetMyTodayWishDataResp) -> Unit) {
    val request = ActExecReq(
        componentInfo = BackendComponentInfo(
            componentID = req.componentId,
            componentType = req.componentType,
            modID = req.modId
        ),
        componentType = req.componentType,
        componentID = req.componentId,
        invocation = mapOf(
            "name" to "/trpc.component_plat.lotteryng.WishPool/GetMyTodayWishData",
            "data" to mapOf(
                "wish_activity_id" to req.wish_activity_id,
            )
        ),
        qualifier_params = listOf()
    )
    ActExecAccessRequester().request(request) { response ->
        KLog.i(TAG, "checkUserLimit rsp: ${response.body} code: ${response.code} tip: ${response.tip}")
        val data = JSONObject(response.body?.data?.data ?: "{}")
        if (data.keySet().isEmpty()) {
            return@request
        }

        val leftWishPoint = data.optInt("left_wish_point")
        val wishData = data.optJSONArray("data")

        callback(GetMyTodayWishDataResp(
            leftWishPoint = leftWishPoint,
            user_recharge_amount = data.optInt("user_recharge_amount"),
            data = wishData?.map { wish ->
                IMyWishData(
                    wishPoolId = wish.optString("wish_pool_id"),
                    propertyId = wish.optString("property_id"),
                    usedWishPoint = wish.optInt("used_wish_point"),
                    allWishPoint = wish.optInt("all_wish_point"),
                    probability = wish.optDouble("probability")
                )
            } ?: listOf()
        ))
    }
}

fun getMyWishList(req: GetMyWishListReq, componentID: String, modID: String, callback: (GetMyWishListRsp) -> Unit) {
    KLog.i(TAG, "getMyWishList req: $req, componentID: $componentID, modID: $modID")
    val request = ActExecReq(
        componentInfo = BackendComponentInfo(
            componentID = componentID,
            componentType = "moka-ui-wish-pool",
            modID = modID
        ),
        componentType = "moka-ui-wish-pool",
        componentID = componentID,
        invocation = mapOf(
            "name" to "/trpc.component_plat.lotteryng.WishPool/GetMyWishList",
            "data" to mapOf(
                "wish_activity_id" to req.wish_activity_id,
                "page_no" to req.pageNo,
                "page_num" to req.pageNum
            )
        ),
        qualifier_params = listOf()
    )
    ActExecAccessRequester().request(request) { response ->
        KLog.i(TAG, "getMyWishList rsp: ${response.body} code: ${response.code} tip: ${response.tip}")

        val rawData = response.body?.data?.data
        KLog.i(TAG, "getMyWishList rawData: $rawData")
        if (rawData.isNullOrBlank()) {
            // 数据为空，直接返回
            return@request
        }

        val originData = try {
            JSONObject(rawData)
        } catch (e: JSONException) {
            KLog.e(TAG, "JSON解析失败: ${e.message}")
            return@request
        }

        if (originData.keySet().isEmpty()) {
            return@request
        }

        val recordList = originData.optJSONArray("record_list")

        callback(
            GetMyWishListRsp(
                code = originData.optInt("code"),
                msg = originData.optString("msg"),
                hasNext = originData.optBoolean("has_next") ?: false,
                recordList = recordList?.map { record ->
                    val property = record.optJSONObject("property")
                    WishRecord(
                        wish_time = convertToMilliseconds(record.optLong("wish_time")),
                        wish_pool_id = record.optString("wish_pool_id"),
                        property_id = record.optString("property_id"),
                        wish_point = record.optInt("wish_point"),
                        wish_status = record.optInt("wish_status"),
                        property = IWishPropertyInfo(
                            id = property?.optString("id") ?: "",
                            name = property?.optString("name") ?: "",
                            icon = property?.optString("icon") ?: "",
                            all_wish_point = property?.optInt("all_wish_point") ?: 0,
                            ext = property?.optString("ext") ?: ""
                        ),
                    )
                } ?: listOf()
            )
        )
    }
}

/**
 * 将时间戳转换成毫秒
 */
private fun convertToMilliseconds(timestamp: Long): Long {
    return if (timestamp < 1_000_000_000_000L) {
        // 认为是秒级时间戳，乘以 1_000 转成微秒
        timestamp * 1_000L
    } else {
        // 认为是毫秒级时间戳
        timestamp
    }
}

fun getMyWishResult(req: GetMyWishResultReq, callback: (GetMyWishResultResp) -> Unit) {
    KLog.i(TAG, "getMyWishResult req: $req")
    val request = ActExecReq(
        componentInfo = BackendComponentInfo(
            componentID = req.componentId,
            componentType = req.componentType,
            modID = req.modId
        ),
        componentType = req.componentType,
        componentID = req.componentId,
        invocation = mapOf(
            "name" to "/trpc.component_plat.lotteryng.WishPool/GetMyWishResult",
            "data" to mapOf(
                "wish_activity_id" to req.wish_activity_id,
            )
        ),
        qualifier_params = listOf()
    )
    ActExecAccessRequester().request(request) { response ->
        KLog.i(TAG, "getMyWishResult rsp: ${response.body} code: ${response.code} tip: ${response.tip}")

        val rawData = response.body?.data?.data
        KLog.i(TAG, "getMyWishResult rawData: $rawData")
        if (rawData.isNullOrBlank()) {
            // 数据为空，直接返回
            return@request
        }

        val data = try {
            JSONObject(rawData)
        } catch (e: JSONException) {
            KLog.e(TAG, "JSON解析失败: ${e.message}")
            return@request
        }

        if (data.keySet().isEmpty()) {
            return@request
        }

        val wishData = data.optJSONObject("wish_pool_result")
        val wishPoolInfo = wishData?.optJSONObject("wish_pool") ?: JSONObject()
        val wishResultList = wishData?.optJSONArray("wish_result_list") ?: JSONArray()

        callback(
            GetMyWishResultResp(
                wishPoolResult = IWishPoolResult(
                    wishPoolInfo = IWishPoolInfo(
                        id = wishPoolInfo.optString("id"),
                        dateTime = wishPoolInfo.optLong("date_time"),
                        canWish = wishPoolInfo.optBoolean("can_wish"),
                        propertyList = wishPoolInfo.optJSONArray("property_list")?.map { property ->
                            IWishPropertyInfo(
                                id = property.optString("id"),
                                name = property.optString("name"),
                                icon = property.optString("icon"),
                                all_wish_point = property.optInt("all_wish_point"),
                                ext = property.optString("ext")
                            )
                        } ?: listOf()
                    ),
                    wishResultList = wishResultList.map { wish ->
                        val property = wish.optJSONObject("property")
                        val user = wish.optJSONObject("user")
                        IWishResult(
                            property = IWishPropertyInfo(
                                id = property?.optString("id") ?: "",
                                name = property?.optString("name") ?: "",
                                icon = property?.optString("icon") ?: "",
                                all_wish_point = property?.optInt("all_wish_point") ?: 0,
                                ext = property?.optString("ext") ?: "",
                            ),
                            user = IWinningUser(
                                name = user?.optString("name") ?: "",
                                icon = user?.optString("icon") ?: "",
                            )
                        )
                    }
                )
            )
        )
    }
}

fun getAllWishResult(req: GetAllWishResultReq, componentID: String, modID: String, callback: (GetAllWishResultRsp) -> Unit) {
    KLog.i(TAG, "getAllWishResult req: $req, modID: $modID, componentID: $componentID")
    val request = ActExecReq(
        componentType = "moka-ui-wish-pool",
        componentID = componentID,
        componentInfo = BackendComponentInfo(
            componentID = componentID,
            componentType = "moka-ui-wish-pool",
            modID = modID
        ),
        invocation = mapOf(
            "name" to "/trpc.component_plat.lotteryng.WishPool/GetAllWishResult",
            "data" to mapOf(
                "wish_activity_id" to req.wish_activity_id,
                "wish_pool_time_list" to req.wish_pool_time_list.toJSONArray()
            )
        ),
        qualifier_params = listOf()
    )
    ActExecAccessRequester().request(request) { response ->
        KLog.i(TAG, "getAllWishResult rsp: ${response.body} code: ${response.code} tip: ${response.tip}")

        val rawData = response.body?.data?.data
        KLog.i(TAG, "getAllWishResult rawData: $rawData")
        if (rawData.isNullOrBlank()) {
            // 数据为空，直接返回空列表
            return@request
        }

        val jsonObject = try {
            JSONObject(rawData)
        } catch (e: JSONException) {
            KLog.e(TAG, "JSON解析失败: ${e.message}")
            return@request
        }

        val result = jsonObject.optJSONArray("wish_pool_result_list")

        callback(GetAllWishResultRsp(
            code = jsonObject.optInt("code"),
            msg = jsonObject.optString("msg"),
            wish_pool_result_list = result?.map { record ->
            val wishPool = record.optJSONObject("wish_pool")
            val wishResultList = record.optJSONArray("wish_result_list")
            IWishPoolResult(
                wishPoolInfo = IWishPoolInfo(
                    canWish = wishPool?.optBoolean("can_wish") ?: false,
                    id = wishPool?.optString("id") ?: "",
                    dateTime = wishPool?.optLong("date_time") ?: 0,
                    propertyList = wishPool?.optJSONArray("property_list")?.map { property ->
                        IWishPropertyInfo(
                            id = property.optString("id"),
                            name = property.optString("name"),
                            icon = property.optString("icon"),
                            all_wish_point = property.optInt("all_wish_point"),
                            ext = property.optString("ext"),
                        )
                    } ?: listOf()
                ),
                wishResultList = wishResultList?.map { wish ->
                    val property = wish.optJSONObject("property")
                    val user = wish.optJSONObject("user")
                    IWishResult(
                        property = IWishPropertyInfo(
                            id = property?.optString("id") ?: "",
                            name = property?.optString("name") ?: "",
                            icon = property?.optString("icon") ?: "",
                            all_wish_point = property?.optInt("all_wish_point") ?: 0,
                            ext = property?.optString("ext") ?: "",
                        ),
                        user = IWinningUser(
                            name = handleAppName(user?.optString("name") ?: ""),
                            icon = user?.optString("icon") ?: "",
                        )
                    )
                } ?: listOf()
            )
        } ?: listOf()),
            )
    }
}

private fun handleAppName(appName: String): String {
    return if (appName.length > 7) {
        appName.substring(0, 7) + "..."
    } else {
        appName
    }
}

fun userWish(req: UserWishReq, componentID: String, modID: String, callback: (UserWishResp) -> Unit) {
    val request = ActExecReq(
        isTest = true,
        componentInfo = BackendComponentInfo(
            componentType = componentType,
            componentID = componentID,
            modID = modID
        ),
        componentType = componentType,
        componentID = componentID,
        invocation = mapOf(
            "name" to "/trpc.component_plat.lotteryng.WishPool/UserWish",
            "data" to mapOf(
                "wish_activity_id" to req.wish_activity_id,
                "wish_pool_id" to req.wish_pool_id,
                "property_id" to req.property_id,
                "use_all_point" to req.use_all_point
            )
        ),
        qualifier_params = listOf()
    )
    KLog.i(TAG, "开始注入我的心愿, wishID: ${req.wish_activity_id}, wishPoolID: ${req.wish_pool_id}, propertyID: ${req.property_id}, useAllPoint: ${req.use_all_point}")
    ActExecAccessRequester().request(request) { response ->
        KLog.i(TAG, "userWish rsp: ${response.body} code: ${response.code} tip: ${response.tip}")

        if (response.code != 0) {
            callback(
                UserWishResp(
                    code = response.code,
                    msg = response.tip,
                )
            )
            return@request
        }

        val rawData = response.body?.data?.data
        KLog.i(TAG, "userWish response.body?.data: ${response.body?.data}")
        KLog.i(TAG, "userWish rawData: $rawData")
        if (rawData.isNullOrBlank()) {
            // 数据为空，直接返回
            return@request
        }

        val data = try {
            JSONObject(rawData)
        } catch (e: JSONException) {
            KLog.e(TAG, "JSON解析失败: ${e.message}")
            return@request
        }

        if (data.keySet().isEmpty()) {
            return@request
        }

        val code = data.optString("code") ?: "-1"
        val msg = data.optString("msg") ?: ""

        callback(UserWishResp(
            code = code.toInt(),
            msg = msg,
        ))
    }
}

fun queryOrderDetail(orderId: String, userType: Int, callback: (JSONObject) -> Unit) {
    val reqBodyJson = JSONObject().apply {
        put("order_id", orderId)
        put("user_type", userType)
    }
    PageRequestEngine.post("https://ovactapi.iwan.yyb.qq.com/trpc.component_plat.property.Property/GetOrderDetail",
        reqBodyJson.toString(), rawData = true) { rawResponse ->
        val headerErrorCode = getTrpcFuncTet(rawResponse)
        if (HttpProtocolInterceptor.enableJceProtocol()) {
            if (headerErrorCode != 0L) {
                val codeConfigManager = ReturnCodeConfigManager<Any>()
                val interfaceKeyword = "/trpc.iwan.team_server.TeamServer/getTeamPoint"
                val configItems = codeConfigManager.getMatchedReturnCodeConfigsByInterface(
                    InterfaceType.Common,
                    interfaceKeyword
                )
                val config = codeConfigManager.getJceProtocolMatchedCodeConfig(
                    headerErrorCode.toString(), configItems)
                KLog.i(TAG, "getTeamPoint failed. error code: $headerErrorCode")
                Utils.BridgeModule().toast(config.tip.ifEmpty { "网络错误，请稍后重试!" })
                callback(JSONObject())
                return@post
            }
        }
        val response = rawResponse?.optJSONObject("body")
        KLog.i(TAG, "doQueryOrderDetail : $response")
        val ret = response?.optInt("ret", -1)
        if (ret == 0) {
            val dataJson = response.optJSONObject("data")
            val orderDetail = dataJson?.optJSONObject("detail") ?: JSONObject()
            callback(orderDetail)
        } else {
            callback(JSONObject())
        }

    }
}