package com.tencent.assistantkuikly.component.moka_components.wish_pool.components

import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ComposeAttr
import com.tencent.kuikly.core.base.ComposeEvent
import com.tencent.kuikly.core.base.ComposeView
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.base.ViewContainer
import com.tencent.kuikly.core.directives.velse
import com.tencent.kuikly.core.directives.velseif
import com.tencent.kuikly.core.directives.vif
import com.tencent.kuikly.core.reactive.handler.observable
import com.tencent.kuikly.core.views.Text
import com.tencent.kuikly.core.views.View

internal class TotalSpendingView: ComposeView<TotalSpendingViewAttr, TotalSpendingViewEvent>() {
    
    override fun createEvent(): TotalSpendingViewEvent {
        return TotalSpendingViewEvent()
    }

    override fun createAttr(): TotalSpendingViewAttr {
        return TotalSpendingViewAttr()
    }

    override fun body(): ViewBuilder {
        val ctx = this
        return {
            View {
                attr {
                    alignItemsCenter()
                    margin(top = 15.wp(), left = 16.wp())
                    width(320.wp())
                    flexDirectionRow()
                }

                Text {
                    attr {
                        fontSize(13.wp())
                        color("#1A1B4B".parseColor())
                        text("我的活动累计消费：")
                    }
                }

                vif({ctx.attr.showLogin}) {
                    Text {
                        attr {
                            fontSize(13.wp())
                            fontWeight700()
                            color("#1A1B4B".parseColor())
                            fontWeightBold()
                            text("登录后可查看")
                        }

                        event {
                            click {
                                ctx.event.onClickPoints.invoke()
                            }
                        }
                    }
                }
                velseif({ctx.attr.needAuthGames}) {
                    Text {
                        attr {
                            fontSize(13.wp())
                            fontWeight700()
                            color("#1A1B4B".parseColor())
                            fontWeightBold()
                            text("授权后查看")
                        }

                        event {
                            click {
                                ctx.event.onClickPoints.invoke()
                            }
                        }
                    }
                }
                velse {
                    Text {
                        attr {
                            fontWeight700()
                            fontSize(13.wp())
                            fontWeight700()
                            color("#1A1B4B".parseColor())
                            text(if (ctx.attr.points == -1) "" else "${ctx.attr.points}")
                        }
                    }
                }
            }
        }
    }
}


internal class TotalSpendingViewAttr : ComposeAttr() {
    var points by observable(0)
    var showLogin by observable(false)
    var needAuthGames by observable(false)
}

internal class TotalSpendingViewEvent : ComposeEvent() {
    var onClickPoints: ()-> Unit = {}

    fun onClickPoints(handle: ()->Unit) {
        onClickPoints = handle
    }
    
}

internal fun ViewContainer<*, *>.TotalSpending(init: TotalSpendingView.() -> Unit) {
    addChild(TotalSpendingView(), init)
}