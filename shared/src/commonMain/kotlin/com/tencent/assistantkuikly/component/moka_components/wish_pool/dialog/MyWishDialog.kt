package com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog

import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ColorStop
import com.tencent.kuikly.core.base.Direction
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.views.Image
import com.tencent.kuikly.core.views.Text
import com.tencent.kuikly.core.views.View

/**
 * 我的心愿单弹窗
 */
object MyWishDialog {

    fun builder(myWishDialogConfig: MyWishDialogConfig, onClose: () -> Unit): ViewBuilder {

        return {
            attr {
                size(282.wp(), 483.wp())
                allCenter()
            }

            event {
                click {}
            }

            // 关闭按钮
            Image {
                attr {
                    alignSelfFlexEnd()
                    size(23.wp(), 23.wp())
                    src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/3GXSCORV.png")
                }
                event {
                    click {
                        onClose.invoke()
                    }
                }
            }
            View {
                attr {
                    size(282.wp(), 461.wp())
                    allCenter()
                }
                // 头部背景
                Image {
                    attr {
                        absolutePosition(top = 0.wp(), left = 11.wp(), right = 11.wp())
                        size(260.wp(), 163.wp())
                        src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/%E6%88%91%E7%9A%84%E5%BF%83%E6%84%BF%E5%8D%95.png")
                    }
                }
                // 主体
                View {
                    // 背景
                    attr {
                        absolutePosition(bottom = 0.wp())
                        size(282.wp(), 400.wp())
                        borderRadius(16.2.wp())
                        backgroundLinearGradient(
                            Direction.TO_BOTTOM,
                            ColorStop("#C3F8FE".parseColor(), 0F),
                            ColorStop("#F2FEFF".parseColor(), 0.12F)
                        )
                    }
                    // 表头 标题
                    View {
                        attr {
                            size(400.wp(), 39.wp())
                            flexDirectionRow()
                            allCenter()
                            justifyContentFlexStart()
                        }
                        Text {
                            attr {
                                marginLeft(31.wp())
                                fontSize(12.wp())
                                text("时间")
                                fontWeight500()
                                color("#203564".parseColor())
                            }
                        }
                        Text {
                            attr {
                                marginLeft(37.wp())
                                fontSize(12.wp())
                                text("心愿")
                                fontWeight500()
                                color("#203564".parseColor())
                            }
                        }
                        Text {
                            attr {
                                marginLeft(33.wp())
                                fontSize(12.wp())
                                text("心愿点")
                                fontWeight500()
                                color("#203564".parseColor())
                            }
                        }
                        Text {
                            attr {
                                marginLeft(32.wp())
                                fontSize(12.wp())
                                text("心愿结果")
                                fontWeight500()
                                color("#203564".parseColor())
                            }
                        }

                    }
                    WishingListItem {
                        attr {
                            height(361.wp())
                            config = myWishDialogConfig
                        }
                    }
                }
            }
        }
    }
}