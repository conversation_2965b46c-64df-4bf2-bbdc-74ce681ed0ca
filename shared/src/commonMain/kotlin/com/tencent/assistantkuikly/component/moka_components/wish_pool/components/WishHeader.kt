package com.tencent.assistantkuikly.component.moka_components.wish_pool.components

import com.tencent.assistantkuikly.component.moka_components.wish_pool.utils.formatWishDate
import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ComposeAttr
import com.tencent.kuikly.core.base.ComposeEvent
import com.tencent.kuikly.core.base.ComposeView
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.base.ViewContainer
import com.tencent.kuikly.core.reactive.handler.observable
import com.tencent.kuikly.core.views.RichText
import com.tencent.kuikly.core.views.Span
import com.tencent.kuikly.core.views.Text
import com.tencent.kuikly.core.views.View

internal class WishHeaderView: ComposeView<WishHeaderViewAttr, WishHeaderViewEvent>() {
    
    override fun createEvent(): WishHeaderViewEvent {
        return WishHeaderViewEvent()
    }

    override fun createAttr(): WishHeaderViewAttr {
        return WishHeaderViewAttr()
    }

    override fun body(): ViewBuilder {
        val ctx = this
        return {
            View {
                attr {
                    margin(top = 20.wp(), left = 16.wp())
                    flexDirectionRow()
                    alignItemsCenter()
                }

                Text {
                    attr {
                        text("活动玩法")
                        fontSize(16.wp())
                        color("rgba(36, 38, 107, 1)".parseColor())
                        fontWeight700()
                    }
                }

                Text {
                    attr {
                        margin(left = 16.wp())
                        text("刷新")
                        fontSize(14.wp())
                        color("rgba(36, 38, 107, 1)".parseColor())
                    }

                    event {
                        click { ctx.event.onRefresh.invoke() }
                    }
                }
            }

            RichText {
                attr {
                    margin(left = 16.wp(), right = 16.wp(), top = 13.wp())
                    fontSize(12.wp())
                    color("rgba(32, 53, 100, 1)".parseColor())
                }

                Span {
                    text("活动期间，在应用宝游戏商城每日每消费100元可许愿")
                }

                Span {
                    fontWeight700()
                    color("rgba(255, 0, 123, 1)".parseColor())
                    text("1")
                }

                Span {
                    text("次，心愿瓶开启许愿时间为每日：")
                }

                Span {
                    fontWeight700()
                    color("rgba(255, 0, 123, 1)".parseColor())
                    text("${formatWishDate(ctx.attr.dailyWishStartTime)}-${formatWishDate(ctx.attr.dailyWishEndTime)}")
                }
            }
        }
    }
}


internal class WishHeaderViewAttr : ComposeAttr() {
    /** 每日心愿开始时间 */
    var dailyWishStartTime by observable("")
    /** 每日心愿结束时间 */
    var dailyWishEndTime by observable("")
}

internal class WishHeaderViewEvent : ComposeEvent() {
    var onRefresh: () -> Unit = {}

    fun onRefresh(handler: ()-> Unit) {
        onRefresh = handler
    }
}

internal fun ViewContainer<*, *>.WishHeader(init: WishHeaderView.() -> Unit) {
    addChild(WishHeaderView(), init)
}