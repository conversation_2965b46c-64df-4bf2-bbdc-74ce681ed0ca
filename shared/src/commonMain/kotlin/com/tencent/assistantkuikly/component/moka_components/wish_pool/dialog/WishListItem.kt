package com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog

import com.tencent.assistantkuikly.base.Utils
import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.WishRecord
import com.tencent.assistantkuikly.report.ReportParams
import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ComposeAttr
import com.tencent.kuikly.core.base.ComposeEvent
import com.tencent.kuikly.core.base.ComposeView
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.base.ViewContainer
import com.tencent.kuikly.core.directives.velse
import com.tencent.kuikly.core.directives.vfor
import com.tencent.kuikly.core.directives.vif
import com.tencent.kuikly.core.views.Image
import com.tencent.kuikly.core.views.List
import com.tencent.kuikly.core.views.Text
import com.tencent.kuikly.core.views.View

internal class WishingListItemView :
    ComposeView<WishingListItemViewAttr, WishingListItemViewEvent>() {

    private val viewModel = MyWishDialogViewModel()

    override fun createAttr(): WishingListItemViewAttr {
        return WishingListItemViewAttr()
    }

    override fun createEvent(): WishingListItemViewEvent {
        return WishingListItemViewEvent()
    }

    override fun created() {
        super.created()
        viewModel.init(attr.config)
    }

    override fun body(): ViewBuilder {
        val ctx = this
        return {
            vif({ ctx.viewModel.wishRecordList.size == 0 && !ctx.viewModel.isLoading }) {
                View {
                    attr {
                        flex(1f)
                        allCenter()
                    }
                    Text {
                        attr {
                            text("暂无注入心愿")
                            color("#606D8B".parseColor())
                            fontSize(13.wp())
                            fontWeight400()
                        }
                    }
                }
            }
            velse {
                List {
                    attr {
                        flex(1F)
                    }
                    vfor({ ctx.viewModel.wishRecordList }) { item ->
                        ctx.itemView(data = item, viewModel = ctx.viewModel).invoke(this)
                    }
                    vif({ ctx.viewModel.wishRecordList.size != 0 }) {
                        // 分割线
                        View {
                            attr {
                                marginLeft(20.wp())
                                size(242.wp(), 1.wp())
                                backgroundColor("#2C2C2C0F".parseColor())
                            }
                        }
                    }
                    View {
                        attr {
                            height(21.wp())
                        }
                    }
                    vif({ ctx.viewModel.finished }) {
                        View {
                            attr {
                                allCenter()
                                marginBottom(20.wp())
                            }
                            Text {
                                attr {
                                    text("没有更多了")
                                    color("#606D8B".parseColor())
                                    fontSize(10.wp())
                                    fontWeight400()
                                }
                            }
                        }
                    }
                    event {
                        scrollEnd { scrollParams ->
                            val height = scrollParams.offsetY + scrollParams.viewHeight
                            if (height - scrollParams.contentHeight <= 50F) {
                                ctx.viewModel.loadMoreMyWishRecord()
                            }
                        }
                    }
                }
            }
        }
    }

    private fun itemView(data: WishRecord, viewModel: MyWishDialogViewModel): ViewBuilder {
        return {
            View {
                attr { }
                // 分割线
                View {
                    attr {
                        marginLeft(20.wp())
                        size(242.wp(), 1.wp())
                        backgroundColor("#2C2C2C0F".parseColor())
                    }
                }
                View {
                    attr {
                        height(66.wp())
                        flexDirectionRow()
                        allCenter()
                        justifyContentFlexStart()
                    }
                    Text {
                        attr {
                            marginLeft(21.wp())
                            fontSize(10.wp())
                            color("#606D8B".parseColor())
                            text(Utils.CalendarModule().formatTime(data.wish_time, "yyyy-MM-dd"))
                        }
                    }
                    Image {
                        attr {
                            marginLeft(12.wp())
                            size(32.wp(), 42.37.wp())
                            src(data.property.icon)
                        }
                    }
                    View {
                        attr {
                            width(29.wp())
                            marginLeft(31.wp())
                            allCenter()
                        }
                        Text {
                            attr {
                                fontSize(10.wp())
                                color("#606D8B".parseColor())
                                text(data.wish_point.toString())
                            }
                        }
                    }
                    Text {
                        attr {
                            val text = viewModel.wishResultText(data.wish_status)
                            marginLeft(45.wp())
                            fontSize(if (text == "中奖") 12.wp() else 10.wp())
                            color(if (text == "中奖") "#FF007B".parseColor() else "#606D8B".parseColor())
                            text(text)
                            if (text == "中奖") fontWeight700() else fontWeight500()
                        }
                    }
                }
            }

        }
    }
}

internal class WishingListItemViewAttr : ComposeAttr() {
    lateinit var config: MyWishDialogConfig
}

internal class WishingListItemViewEvent : ComposeEvent() {

}

internal fun ViewContainer<*, *>.WishingListItem(init: WishingListItemView.() -> Unit) {
    addChild(WishingListItemView(), init)
}

data class MyWishDialogConfig(
    val activityId: String,
    val modId: String,
    val reportParams: ReportParams
)
