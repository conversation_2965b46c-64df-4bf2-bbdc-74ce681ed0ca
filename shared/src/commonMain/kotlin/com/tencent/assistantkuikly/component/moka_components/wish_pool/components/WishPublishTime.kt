package com.tencent.assistantkuikly.component.moka_components.wish_pool.components

import com.tencent.assistantkuikly.component.moka_components.wish_pool.utils.formatWishDate
import com.tencent.assistantkuikly.core.message.MessageCenter
import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ComposeView
import com.tencent.kuikly.core.base.ComposeAttr
import com.tencent.kuikly.core.base.ComposeEvent
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.base.ViewContainer
import com.tencent.kuikly.core.nvi.serialization.json.JSONObject
import com.tencent.kuikly.core.reactive.handler.observable
import com.tencent.kuikly.core.views.Image
import com.tencent.kuikly.core.views.RichText
import com.tencent.kuikly.core.views.Span

internal class WishPublishTimeView: ComposeView<WishPublishTimeViewAttr, WishPublishTimeViewEvent>() {
    
    override fun createEvent(): WishPublishTimeViewEvent {
        return WishPublishTimeViewEvent()
    }

    override fun createAttr(): WishPublishTimeViewAttr {
        return WishPublishTimeViewAttr()
    }

    override fun body(): ViewBuilder {
        val ctx = this
        return {
            attr {
                margin(left = 16.wp(), right = 16.wp(), top = 10.wp())
                flexDirectionRow()
                alignItemsCenter()
                justifyContentSpaceBetween()
            }

            RichText {
                attr {
                    fontSize(14.wp())
                    color("rgba(26, 27, 75, 1)".parseColor())
                    fontWeight500()
                }

                Span {
                    text("心愿揭晓公布时间：")
                }

                Span {
                    fontWeight700()
                    color("rgba(255, 0, 123, 1)".parseColor())
                    text(formatWishDate(ctx.attr.dailyWishPublishTime))
                }
            }

            Image {
                attr {
                    size(60.wp(), 26.wp())
                    src(ctx.attr.subscribeIcon)
                }

                event {
                    click {
                        MessageCenter.postNotify("handleOnSubscribeClick", JSONObject())
                    }
                }
            }
        }
    }
}


internal class WishPublishTimeViewAttr : ComposeAttr() {
    /** 每日心愿公布时间 */
    var dailyWishPublishTime by observable("")

    /** 订阅按钮图片*/
    var subscribeIcon by observable("")
}

internal class WishPublishTimeViewEvent : ComposeEvent() {
    
}

internal fun ViewContainer<*, *>.WishPublishTime(init: WishPublishTimeView.() -> Unit) {
    addChild(WishPublishTimeView(), init)
}