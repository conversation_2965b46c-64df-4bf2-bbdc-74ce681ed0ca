package com.tencent.assistantkuikly.component.moka_components.wish_pool.components

import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ComposeView
import com.tencent.kuikly.core.base.ComposeAttr
import com.tencent.kuikly.core.base.ComposeEvent
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.base.ViewContainer
import com.tencent.kuikly.core.views.Image
import com.tencent.kuikly.core.views.Text
import com.tencent.kuikly.core.views.View

internal class MyRewardView: ComposeView<MyRewardViewAttr, MyRewardViewEvent>() {
    
    override fun createEvent(): MyRewardViewEvent {
        return MyRewardViewEvent()
    }

    override fun createAttr(): MyRewardViewAttr {
        return MyRewardViewAttr()
    }

    override fun body(): ViewBuilder {
        val ctx = this
        return {
            View {
                attr {
                    alignItemsCenter()
                    justifyContentCenter()
                    margin(top = 15.wp(), left = 16.wp())
                    width(320.wp())
                    flexDirectionRow()
                }

                Text {
                    attr {
                        fontSize(14.wp())
                        color("rgba(26, 27, 75, 1)".parseColor())
                        text("我的奖品")
                    }
                }

                Image {
                    attr {
                        margin(left = 3.wp())
                        size(5.wp(), 8.wp())
                        src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/xDJBzcoT.png")
                    }
                }

                event {
                    click {
                        ctx.event.onShowMyRewardList.invoke()
                    }
                }
            }
        }
    }
}


internal class MyRewardViewAttr : ComposeAttr() {

}

internal class MyRewardViewEvent : ComposeEvent() {
    var onShowMyRewardList: ()-> Unit = {}

    fun onShowMyRewardList(handler: ()-> Unit) {
        onShowMyRewardList = handler
    }
}

internal fun ViewContainer<*, *>.MyReward(init: MyRewardView.() -> Unit) {
    addChild(MyRewardView(), init)
}