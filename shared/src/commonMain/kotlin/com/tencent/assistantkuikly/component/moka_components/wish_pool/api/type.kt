package com.tencent.assistantkuikly.component.moka_components.wish_pool.api


/** 获取心愿池信息请求体 */
data class GetWishPoolDataReq (
    /** 组件类型*/
    var componentType: String = "",
    /** 组件Id*/
    var componentId: String = "",
    /** 模块Id*/
    var modId: String = "",
    /** 步长 为0 则只返回当天,为1则返回昨天，今天，明天三天的数据 */
    var step_num: Int,
    /** 心愿活动 ID  */
    var wish_activity_id: String
)

/** 获取心愿池信息请求体 */
data class GetWishPoolDataRespData (
    /** 心愿活动信息 */
    var data: IWishPoolActivityInfo,
    /** 参与活动游戏信息 */
    var appInfo: List<IAppInfo>,
    /** 服务器当前时间戳 */
    var serverTime: Long
)

/** 获取用户当天许愿情况请求体 */
data class GetMyTodayWishDataReq (
    /** 组件类型*/
    var componentType: String = "",
    /** 组件Id*/
    var componentId: String = "",
    /** 模块Id*/
    var modId: String = "",
    /** 心愿活动 ID  */
    var wish_activity_id: String
)

/** 获取用户当天许愿情况响应体 */
data class GetMyTodayWishDataResp (
    /** 用户剩余心愿点 */
    var leftWishPoint: Int,
    /** 我的心愿物品 */
    var data: List<IMyWishData>,
    /** 用户充值金额 */
    var user_recharge_amount: Int
)

/** 获取用户心愿列表*/
data class GetMyWishListReq (
    /** 许愿活动id*/
    var wish_activity_id: String,
    /** 翻页信息,获取第几页*/
    var pageNo: Int,
    /** 翻页信息,每一页获取条数*/
    var pageNum: Int
)

data class GetMyWishListRsp(
    var hasNext: Boolean,
    val recordList: List<WishRecord>,
    val code: Int,
    val msg: String
)

/** 获取所有的心愿抽奖结果请求*/
data class GetAllWishResultReq (
    /** 许愿活动id */
    var wish_activity_id: String,
    /** // 需要的许愿池的时间列表，比如填 昨天 12:00的时间，则返回昨天的许愿池中奖记录 */
    var wish_pool_time_list: List<Long>
)

/** 取所有的心愿抽奖结果数据*/
data class GetAllWishResultRsp(
    var code: Int,
    var msg: String,
    var wish_pool_result_list: List<IWishPoolResult>
)

/** 获取我的心愿抽奖结果请求体 */
data class GetMyWishResultReq (
    /** 组件类型*/
    var componentType: String = "",
    /** 组件Id*/
    var componentId: String = "",
    /** 模块Id*/
    var modId: String = "",
    /** 心愿活动 ID */
    var wish_activity_id: String
)

/** 获取我的心愿抽奖结果响应体 */
data class GetMyWishResultResp (
    /** 许愿活动 ID */
    var wishPoolResult: IWishPoolResult
)

/** 用户许愿 */
data class UserWishReq (
    /** 许愿活动 ID */
    var wish_activity_id: String,
    /** 心愿池 ID */
    var wish_pool_id: String,
    /** 心愿物品 ID */
    var property_id: String,
    /** 是否使用所有点数 */
    var use_all_point: Boolean
)

/** 用户许愿 */
// eslint-disable-next-line @typescript-eslint/no-empty-interface
data class  UserWishResp (
    val code: Int,
    val msg: String
)

/** 游戏信息 */
data class IAppInfo (
    /** 游戏 AppID */
    var appId: Int,
    /** 游戏图标 */
    var appIcon: String,
    /** 游戏名称 */
    var appName: String
)

/** 许愿活动信息 */
data class IWishPoolActivityInfo (
    var baseInfo: IWishPoolActivityBaseInfo = IWishPoolActivityBaseInfo(),
    /** 心愿活动 ID */
    var id: String = "",
    /** 心愿活动开始时间 */
    var startTime: Long = 0L,
    /** 心愿活动结束时间 */
    var endTime: Long = 0L,
    /** 心愿池列表 */
    var wishPoolList: List<IWishPoolInfo> = emptyList()
)

data class IWishPoolActivityBaseInfo(
    var wishEndTime: String = "",
    var wishLotteryTime: String = "",
    val wishStartTime: String = ""
)

/** 许愿池信息 */
data class IWishPoolInfo (
    /** 许愿池 ID */
    var id: String,
    /** 活动所属时间，以当天 12 点时间戳为准 */
    var dateTime: Long,
    var canWish: Boolean,
    /** 许愿物品列表 */
    var propertyList: List<IWishPropertyInfo>

)

/** 许愿物品 */
data class IWishPropertyInfo (
    /** 许愿物品 ID */
    var id: String = "",
    /** 许愿物品名称 */
    var name: String = "",
    /** 许愿物品图标 */
    var icon: String = "",
    /** 许愿商品心愿点*/
    var all_wish_point: Int = 9999,
    /** 许愿物品额外信息，主要是关联的活动物品信息 */
    var ext: String = ""
)

/** 我的心愿物品 */
data class IMyWishData (
    /** 许愿池 ID */
    var wishPoolId: String,
    /** 许愿商品 ID */
    var propertyId: String,
    /** 已使用心愿点 */
    var usedWishPoint: Int,
    /** 所有投入的心愿点 */
    var allWishPoint: Int,
    /** 中奖概率 */
    var probability: Double,
    /** 许愿物品*/
    var property: IWishPropertyInfo? = null,
)


/** 我的心愿结果 */
data class IWishPoolResult (
    /** 心愿池信息 */
    var wishPoolInfo: IWishPoolInfo,
    /** 所有的心愿抽奖结果  */
    var wishResultList: List<IWishResult>
)


/** 心愿中奖结果 */
data class IWishResult (
    /** 中奖物品 */
    var property: IWishPropertyInfo,
    /** 中奖用户 */
    var user: IWinningUser
)

/** 中奖用户信息 */
data class IWinningUser (
    /** 用户名称 */
    var name: String,
    /** 用户图标 */
    var icon: String
)

/** 心愿记录*/
data class WishRecord(
    /** 许愿池id */
    var wish_pool_id: String,
    /** 许愿商品id */
    var property_id: String,
    /** 使用的心愿点 */
    var wish_point: Int,
    /** 许愿时间 */
    var wish_time: Long,
    /** 许愿状态*/
    var wish_status: Int,
    /** 许愿商品信息*/
    var property: IWishPropertyInfo
)