package com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog

import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ColorStop
import com.tencent.kuikly.core.base.Direction
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.views.Image
import com.tencent.kuikly.core.views.View

object WishHistoryDialog {

    fun builder(wishHistoryItemConfig: WishHistoryItemConfig,onClose: () -> Unit): ViewBuilder {


        return {
            attr {
                size(282.wp(), 483.wp())
                allCenter()
            }

            event {
                click {}
            }

            // 关闭按钮
            Image {
                attr {
                    alignSelfFlexEnd()
                    size(23.wp(), 23.wp())
                    src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/3GXSCORV.png")
                }
                event {
                    click {
                        onClose.invoke()
                    }
                }
            }
            View {
                attr {
                    size(282.wp(), 461.wp())
                    allCenter()
                }
                // 头部背景
                Image {
                    attr {
                        absolutePosition(top = 0.wp(), left = 11.wp(), right = 11.wp())
                        size(260.wp(), 163.wp())
                        src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/%E5%BE%80%E6%9C%9F%E8%83%8C%E6%99%AF.png")
                    }
                }
                // 主体
                View {
                    // 背景
                    attr {
                        absolutePosition(bottom = 0.wp())
                        size(282.wp(), 400.wp())
                        borderRadius(16.2.wp())
                        backgroundLinearGradient(
                            Direction.TO_BOTTOM,
                            ColorStop("#C3F8FE".parseColor(), 0F),
                            ColorStop("#F2FEFF".parseColor(), 0.15F)
                        )
                    }

                    WishHistoryItem {
                        attr {
                            height(400.wp())
                            config = wishHistoryItemConfig
                        }
                    }
                }

            }
        }
    }
}