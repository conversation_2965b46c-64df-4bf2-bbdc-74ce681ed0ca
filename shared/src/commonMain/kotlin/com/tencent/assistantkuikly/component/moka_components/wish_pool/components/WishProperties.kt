package com.tencent.assistantkuikly.component.moka_components.wish_pool.components

import com.tencent.assistantkuikly.base.Utils
import com.tencent.assistantkuikly.component.moka_components.task_obtain.events.TaskObtainEvent
import com.tencent.assistantkuikly.component.moka_components.wish_pool.SelectableProperty
import com.tencent.assistantkuikly.component.moka_components.wish_pool.WishWallPanel
import com.tencent.assistantkuikly.core.message.MessageCenter
import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ColorStop
import com.tencent.kuikly.core.base.ComposeAttr
import com.tencent.kuikly.core.base.ComposeEvent
import com.tencent.kuikly.core.base.ComposeView
import com.tencent.kuikly.core.base.Direction
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.base.ViewContainer
import com.tencent.kuikly.core.base.ViewRef
import com.tencent.kuikly.core.directives.vfor
import com.tencent.kuikly.core.directives.vforIndex
import com.tencent.kuikly.core.directives.vif
import com.tencent.kuikly.core.log.KLog
import com.tencent.kuikly.core.nvi.serialization.json.JSONObject
import com.tencent.kuikly.core.reactive.handler.observable
import com.tencent.kuikly.core.reactive.handler.observableList
import com.tencent.kuikly.core.views.Image
import com.tencent.kuikly.core.views.PageList
import com.tencent.kuikly.core.views.PageListView
import com.tencent.kuikly.core.views.RichText
import com.tencent.kuikly.core.views.ScrollParams
import com.tencent.kuikly.core.views.Span
import com.tencent.kuikly.core.views.TabItem
import com.tencent.kuikly.core.views.TabItemView
import com.tencent.kuikly.core.views.Tabs
import com.tencent.kuikly.core.views.Text
import com.tencent.kuikly.core.views.View

internal class WishPropertiesView: ComposeView<WishPropertiesViewAttr, WishPropertiesViewEvent>() {
    private var pageListRef: ViewRef<PageListView<*, *>>? = null
    private var scrollParams: ScrollParams? by observable(null)
    private var currentPageIndex by observable(0)
    private var isSelectTargetWishPanel by observable(false)

    override fun created() {
        super.created()
        MessageCenter.addNotify("selectTargetWishPanel") {
            val pageIndex = it?.optInt("pageIndex") ?: 0
            currentPageIndex = pageIndex
            pageListRef?.view?.scrollToPageIndex(pageIndex, false)
            // 解决刷新状态 page不滑动时，导致scrollParams没有赋值，tab选中项不更新的问题。
            isSelectTargetWishPanel = true
        }
    }
    
    override fun createEvent(): WishPropertiesViewEvent {
        return WishPropertiesViewEvent()
    }

    override fun createAttr(): WishPropertiesViewAttr {
        return WishPropertiesViewAttr()
    }

    override fun body(): ViewBuilder {
        val ctx = this
        return {
            Tabs {
                attr {
                    margin(left = 8.wp(), top = 12.wp())
                    size(288.34.wp(), 24.58.wp()) // 横向布局，务必指定高度
                    defaultInitIndex(0)
                    ctx.scrollParams?.also {
                        scrollParams(it)
                    }
                    backgroundColor("rgba(241, 249, 255, 1)".parseColor())
                    borderRadius(5.wp())
                }
                vforIndex({ ctx.attr.walls }) { wall, index, _ ->
                    TabItem { state ->
                        ctx.changeSelectState(state, index)
                        attr {
                            margin(top = 1.89.wp(), left = 2.wp(), right = 2.wp())
                            size(<EMAIL>(), 20.8.wp())
                        }

                        vif({state.selected}) {
                            View {
                                attr {
                                    size(<EMAIL>(), 20.8.wp())
                                    borderRadius(5.wp())
                                    backgroundLinearGradient(Direction.TO_BOTTOM, ColorStop("rgba(50, 221, 255, 1)".parseColor(), 0F),
                                        ColorStop("rgba(25, 164, 255, 1)".parseColor(), 1F))
                                }
                            }
                        }

                        Text {
                            attr {
                                color(if (state.selected) "rgba(255, 255, 255, 1)".parseColor() else "rgba(136, 161, 192, 1)".parseColor())
                                lineHeight(20.8.wp())
                                fontSize(14.wp())
                                size(<EMAIL>(), 20.8.wp())
                                textAlignCenter()
                                absolutePosition()
                                text(Utils.CalendarModule().formatTime(wall.dateTime * 1000, "M.d"))
                            }
                        }

                        event {
                            click {
                                KLog.i("WishProperties", "click Tab")
                                ctx.pageListRef?.view?.scrollToPageIndex(ctx.attr.walls.indexOf(wall), false)
                            }
                        }
                    }
                }
            }

            PageList {
                attr {
                    margin(top = 10.wp())
                    alignSelfCenter()
                    flexDirectionRow()
                    pageItemWidth(288.34.wp())
                    pageItemHeight(234.wp())
                    defaultPageIndex(0)
                    scrollEnable(true)
                }
                ref {
                    ctx.pageListRef = it
                }
                event {
                    scroll {
                        ctx.scrollParams = it
                    }

                    pageIndexDidChanged {
                        val index = (it as JSONObject).optInt("index")
                        ctx.event.onPageSelected(index)
                    }
                }
                vfor({ctx.attr.walls}) { item ->
                    View {
                        ctx.wallPanel(item).invoke(this)
                    }
                }
            }
        }
    }

    private fun tabSize(): Float {
        return (288.34.wp() - (4.wp() * attr.walls.size))/ attr.walls.size
    }

    private fun changeSelectState(state: TabItemView.ItemState, index: Int) {
        if (!isSelectTargetWishPanel) {
            return
        }
        state.selected = currentPageIndex == index
    }

    private fun wallPanel(wishWallPanel: WishWallPanel): ViewBuilder {
        return {
            View {
                attr {
                    flexDirectionRow()
                    justifyContentSpaceBetween()
                }

                wishWallPanel.propertyList.take(3).forEach { property ->
                    <EMAIL>(wishWallPanel, property).invoke(this)
                }
            }

            View {
                attr {
                    margin(top = 10.wp())
                    flexDirectionRow()
                    justifyContentSpaceBetween()
                }

                wishWallPanel.propertyList.takeLast(3).forEach { property ->
                    <EMAIL>(wishWallPanel, property).invoke(this)
                }
            }
        }
    }

    private fun propertyItem(wishWallPanel: WishWallPanel, property: SelectableProperty): ViewBuilder {
        return {
            View {
                attr {
                    size(89.42.wp(), 104.84.wp())
                }

                Image {
                    attr {
                        absolutePositionAllZero()
                        size(89.42.wp(), 104.84.wp())
                        src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/9CeLH3hR.png")
                    }
                }

                Text {
                    attr {
                        textAlignCenter()
                        color("rgba(46, 38, 165, 1)".parseColor())
                        alignSelfCenter()
                        margin(top = 5.wp())
                        size(width = 83.wp(), height = 12.wp())
                        text(property.property?.name ?: "")
                    }
                }

                Image {
                    attr {
                        size(55.wp(), 68.wp())
                        margin(left = 20.76.wp(), top = 17.wp())
                        src(property.property?.icon ?: "")
                    }
                }

                View {
                    attr {
                        size(82.2.wp(), 16.4.wp())
                        absolutePosition(bottom = 2.89.wp(), left = 2.92.wp())
                    }
                    Image {
                        attr {
                            absolutePositionAllZero()
                            size(82.2.wp(), 16.4.wp())
                            src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/8Jf22NxM.png")
                        }
                    }

                    RichText {
                        attr {
                            margin(left = 3.wp(), top = 3.wp())
                        }

                        Span {
                            color("rgba(24, 22, 61, 1)".parseColor())
                            fontSize(6.wp())
                            fontWeight500()
                            text("已注入")
                        }

                        Span {
                            color("rgba(255, 0, 123, 1)".parseColor())
                            fontSize(9.wp())
                            fontWeight500()
                            text("${property.property?.all_wish_point}点心愿")
                        }
                    }
                }

                vif({wishWallPanel.canWish}) {
                    Image {
                        attr {
                            size(16.85.wp(), 16.85.wp())
                            absolutePosition(right = 0F, top = 10F)
                            src(if (property.selected) {
                                "https://cdn.yyb.gtimg.com/wupload/xy/yybtech/P1Q6njzG.png"
                            } else {
                                "https://cdn.yyb.gtimg.com/wupload/xy/yybtech/m3c46inq.png"
                            })
                        }
                    }
                }

                event {
                    click {
                        if (wishWallPanel.canWish) {
                            if (property.selected) {
                                <EMAIL>(
                                    property.property?.id ?: ""
                                )
                            } else {
                                <EMAIL>(
                                    property.property?.id ?: ""
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}


internal class WishPropertiesViewAttr : ComposeAttr() {
    var walls by observableList<WishWallPanel>()
}

internal class WishPropertiesViewEvent : ComposeEvent() {
    var onSelectedProperty: (id: String) -> Unit = {}
    var onUnSelectedProperty: (id: String) -> Unit = {}
    var onPageSelected: (index: Int) -> Unit = {}

    fun onSelectedProperty(handler: (String) -> Unit) {
        onSelectedProperty = handler
    }

    fun onUnSelectedProperty(handler: (String) -> Unit) {
        onUnSelectedProperty = handler
    }

    fun onPageSelected(handler: (Int) -> Unit) {
        onPageSelected = handler
    }
}

internal fun ViewContainer<*, *>.WishProperties(init: WishPropertiesView.() -> Unit) {
    addChild(WishPropertiesView(), init)
}