package com.tencent.assistantkuikly.component.moka_components.wish_pool.dialog

import com.tencent.assistantkuikly.component.moka_components.wish_pool.api.IWishResult
import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ColorStop
import com.tencent.kuikly.core.base.Direction
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.views.Image
import com.tencent.kuikly.core.views.Text
import com.tencent.kuikly.core.views.View

/**
 * 中奖弹窗
 */
object WishRewardDialog {

    fun builder(wishResult: List<IWishResult>, onClose: () -> Unit, onClick: () -> Unit): ViewBuilder {

        return {
            event {
                click {}
            }

            // 关闭按钮
            Image {
                attr {
                    alignSelfFlexEnd()
                    size(23.wp(), 23.wp())
                    src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/3GXSCORV.png")
                }
                event {
                    click {
                        onClose.invoke()
                    }
                }
            }
            View {
                attr {
                    marginTop((-15).wp())
                    size(282.wp(), 270.wp())
                    allCenter()
                }

                // 背景
                View {
                    // 背景
                    attr {
                        absolutePosition(bottom = 0.wp())
                        size(282.wp(), 234.wp())
                        borderRadius(16.2.wp())
                        backgroundLinearGradient(
                            Direction.TO_BOTTOM,
                            ColorStop("#C3F8FE".parseColor(), 0F),
                            ColorStop("#F2FEFF".parseColor(), 0.15F)
                        )
                        allCenter()
                    }
                    Text {
                        attr {
                            marginTop(109.wp())
                            height(31.wp())
                            text("恭喜您获得${wishResult.firstOrNull()?.property?.name}${if (wishResult.size > 1) "等多个" else ""}盲盒")
                            color("#FF007B".parseColor())
                            fontSize(16.wp())
                            fontWeight700()
                            fontWeightBold()
                        }
                    }
                    Text {
                        attr {
                            text("填写地址后15天内发货")
                            color("#203564".parseColor())
                            fontSize(15.wp())
                            fontWeight400()
                        }
                    }
                    Image {
                        attr {
                            marginTop(11.wp())
                            size(203.wp(), 42.wp())
                            src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/1WGhFHvc.png")
                        }
                        event {
                            click {
                                onClick.invoke()
                            }
                        }
                    }
                }

                // 头部背景
                wishResult.firstOrNull()?.let {
                    val property = it.property
                    View {
                        attr {
                            absolutePosition(top = 10.wp(), left = 85.5.wp())
                            size(111.wp(), 130.wp())
                        }

                        Image {
                            attr {
                                absolutePositionAllZero()
                                size(111.wp(), 130.wp())
                                src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/9CeLH3hR.png")
                            }
                        }

                        Text {
                            attr {
                                textAlignCenter()
                                color("rgba(46, 38, 165, 1)".parseColor())
                                alignSelfCenter()
                                margin(top = 5.wp())
                                size(width = 83.wp(), height = 12.wp())
                                text(property.name)
                            }
                        }

                        Image {
                            attr {
                                size(62.79.wp(), 83.15.wp())
                                margin(left = 23.wp(), top = 20.wp())
                                src(property.icon)
                            }
                        }

                        View {
                            attr {
                                size(102.wp(), 19.wp())
                                absolutePosition(bottom = 3.36.wp(), left = 3.36.wp())
                            }
                            Image {
                                attr {
                                    absolutePositionAllZero()
                                    size(102.wp(), 19.wp())
                                    src("https://cdn.yyb.gtimg.com/wupload/xy/yybtech/8Jf22NxM.png")
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}