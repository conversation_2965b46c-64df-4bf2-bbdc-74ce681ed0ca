package com.tencent.assistantkuikly.component.moka_components.wish_pool.components

import com.tencent.assistantkuikly.utils.parseColor
import com.tencent.assistantkuikly.utils.wp
import com.tencent.kuikly.core.base.ColorStop
import com.tencent.kuikly.core.base.ComposeView
import com.tencent.kuikly.core.base.ComposeAttr
import com.tencent.kuikly.core.base.ComposeEvent
import com.tencent.kuikly.core.base.Direction
import com.tencent.kuikly.core.base.ViewBuilder
import com.tencent.kuikly.core.base.ViewContainer
import com.tencent.kuikly.core.reactive.handler.observable
import com.tencent.kuikly.core.views.Image
import com.tencent.kuikly.core.views.compose.Button

internal class DoWishPropertyView: ComposeView<DoWishPropertyViewAttr, DoWishPropertyViewEvent>() {
    
    override fun createEvent(): DoWishPropertyViewEvent {
        return DoWishPropertyViewEvent()
    }

    override fun createAttr(): DoWishPropertyViewAttr {
        return DoWishPropertyViewAttr()
    }

    override fun body(): ViewBuilder {
        val ctx = this
        return {
            attr {
                alignSelfCenter()
                margin(top = 5.wp(), bottom = 16.wp())
                width(288.34.wp())
                flexDirectionRow()
                justifyContentSpaceBetween()
            }

            Button {
                attr {
                    size(137.62.wp(), 38.wp())
                    borderRadius(296.wp())
                    backgroundLinearGradient(
                        direction = Direction.TO_BOTTOM,
                        *if (ctx.attr.wishable) {
                            arrayOf(
                                ColorStop("#FFEC89".parseColor(), 0f),
                                ColorStop("#FFD417".parseColor(), 0.5f)
                            )
                        } else {
                            arrayOf(
                                ColorStop("#F0F0F0".parseColor(), 0f),
                                ColorStop("#BBBBBB".parseColor(), 0.5f)
                            )
                        }
                    )
                    titleAttr {
                        text("注入1点心愿点")
                        fontWeight400()
                        fontWeightBold()
                        fontSize(15.wp())
                        color(if (ctx.attr.wishable) "#0F0F0F".parseColor() else "#4D4D4D".parseColor())
                    }
                }
                event {
                    click {
                        ctx.event.onClickWishOnePoints()
                    }
                }
            }

            Button {
                attr {
                    size(137.62.wp(), 38.wp())
                    borderRadius(296.wp())
                    backgroundLinearGradient(
                        direction = Direction.TO_BOTTOM,
                        *if (ctx.attr.wishable) {
                            arrayOf(
                                ColorStop("#35C6FF".parseColor(), 0f),
                                ColorStop("#0483FB".parseColor(), 0.5f)
                            )
                        } else {
                            arrayOf(
                                ColorStop("#F0F0F0".parseColor(), 0f),
                                ColorStop("#BBBBBB".parseColor(), 0.5f)
                            )
                        }
                    )
                    titleAttr {
                        text(if (ctx.attr.leftWishPoint <= 100) "注入全部心愿点" else "注入100点心愿点")
                        fontWeight400()
                        fontWeightBold()
                        fontSize(15.wp())
                        color(if (ctx.attr.wishable) "#FFFFFF".parseColor() else "#4D4D4D".parseColor())
                    }
                }
                event {
                    click {
                        ctx.event.onClickWishAllPoints()
                    }
                }
            }
        }
    }
}


internal class DoWishPropertyViewAttr : ComposeAttr() {
    var wishable by observable(true)
    var leftWishPoint by observable(0)
}

internal class DoWishPropertyViewEvent : ComposeEvent() {
    var onClickWishOnePoints: ()-> Unit = {}
    var onClickWishAllPoints: ()-> Unit = {}

    fun onClickWishOnePoints(handler: ()-> Unit ) {
        onClickWishOnePoints = handler
    }

    fun onClickWishAllPoints(handler: ()-> Unit ) {
        onClickWishAllPoints = handler
    }
}

internal fun ViewContainer<*, *>.DoWishProperty(init: DoWishPropertyView.() -> Unit) {
    addChild(DoWishPropertyView(), init)
}